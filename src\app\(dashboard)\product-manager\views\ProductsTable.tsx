"use client";
import React, { useState } from "react";
import {
  DataGrid,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridColDef,
} from "@mui/x-data-grid";

import "../../../styles/global-table-ui-styles.css";
import TextMui from "src/app/components/ui/Text";
import Icon from "src/app/components/ui/icon";
import Image from "next/image";
import { Product } from "src/services/produc-manager/dtos/get-all-products-table-response.dto";

import { EFieldProductsTable } from "../components/Table/enum/products-table.enum";
import ColumnHeader from "../components/Table/components/ColumnHeader";
import HeaderTable from "../components/Table/components/HeaderTable";
import { useGetProductsPagination } from "src/hooks/productManager/useGetProductsPagination";
import CustomPagination from "src/app/components/custom/CustomPagination";
import { Menu, MenuItem } from "@mui/material";
import { useDeleteProduct } from "src/hooks/productManager/useDeleteProduct";
import { useDuplicateProduct } from "src/hooks/productManager/useDuplicateProduct";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export interface ProductsTableViewProps {
  products: Product[];
}

const ProductsTableView = () => {
  // ** Next Hooks

  const router = useRouter();
  // ** Custom Hooks
  const {
    loading,
    productManagerPagination,
    setProductManagerPagination,
    pagination,
    products,
    getProductsPagination,
    setLoading,
  } = useGetProductsPagination();

  const { deleteProduct } = useDeleteProduct();
  const { duplicateProduct } = useDuplicateProduct();

  // ** Local States

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setSelectedProduct(null);
    setAnchorEl(null);
  };

  const onSearchFilters = (key: string, value: string | string[]) => {
    setProductManagerPagination((prevState) => {
      const existingFilterIndex = prevState.filters.findIndex(
        (filter) => filter.type === key
      );
      if (existingFilterIndex === -1) {
        // If the filter does not exist, add it to the end of the array
        if (value !== "") {
          return {
            ...prevState,
            filters: [...prevState.filters, { type: key, value }],
            pagination: {
              ...productManagerPagination.pagination,
              currentPage: 1,
            },
          };
        } else {
          // If the value is an empty string, do not add a new filter
          return prevState;
        }
      } else {
        // If the filter exists, replace it with the new value or remove it if the value is an empty string
        const newFilters = prevState.filters.filter(
          (filter, index) => index !== existingFilterIndex || value !== ""
        );

        if (value !== "") {
          newFilters[existingFilterIndex] = { type: key, value };
        }

        return {
          ...prevState,
          filters: newFilters,
          pagination: {
            ...productManagerPagination.pagination,
            currentPage: 1,
          },
        };
      }
    });
  };

  const handleDelete = async (id: number) => {
    const response = await deleteProduct(id);
    console.log("Eliminar producto con ID:", id);
    console.log("Respuesta de eliminar producto:", response);
    if (response) {
      setLoading(true);
      await getProductsPagination({ ...productManagerPagination });
    }
    handleClose();
  };

  const handleEdit = (id: string) => {
    handleClose();
    router.push(`/product-manager/edit-product/${id}`);
  };

  const handleDuplicate = async (id: number) => {
    const response = await duplicateProduct(id);

    if (response.success) {
      toast.success("Producto dupliado", {
        style: {
          fontFamily: "Poppins",
          fontWeight: "bold",
          color: "rgb(103 202 35)",
          background: "rgb(238 251 229)",
          border: "1px solid rgb(238 251 229)",
        },
      });
      setLoading(true);
      await getProductsPagination({ ...productManagerPagination });
    } else {
      toast.error("Error al duplicar el producto", {
        style: {
          fontFamily: "Poppins",
          fontWeight: "bold",
          color: "#e53935",
          background: "#ffebee",
          border: "1px solid #ffebee",
        },
      });
    }
    handleClose();
  };

  const ModalActions = () => {
    return (
      <>
        <Menu
          disableScrollLock={true}
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          <MenuItem
            onClick={() => {
              if (selectedProduct !== null) {
                handleEdit(selectedProduct.toString());
              }
            }}
            sx={{
              minWidth: "172px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <TextMui text="Editar" type="subtitle" />
            <Icon icon="ic:outline-edit" fontSize={24} color="#7D7D7D" />
          </MenuItem>

          <MenuItem
            onClick={() => {
              if (selectedProduct !== null) {
                handleDelete(selectedProduct);
              }
            }}
            sx={{
              minWidth: "172px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <TextMui text="Eliminar" type="subtitle" />
            <Icon
              icon="ic:baseline-delete-outline"
              fontSize={24}
              color="#7D7D7D"
            />
          </MenuItem>
          <MenuItem
            onClick={() => {
              if (selectedProduct !== null) {
                handleDuplicate(selectedProduct);
              }
            }}
            sx={{
              minWidth: "172px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <TextMui text="Duplicar" type="subtitle" />
            <Icon
              icon="ic:outline-content-copy"
              fontSize={24}
              color="#7D7D7D"
            />
          </MenuItem>
        </Menu>
      </>
    );
  };

  const onPageChange = (
    _event: React.ChangeEvent<unknown> | null,
    value: number
  ) => {
    setProductManagerPagination({
      ...productManagerPagination,

      pagination: {
        ...productManagerPagination.pagination,
        currentPage: value,
      },
    });
  };

  const extractUrl = (text: string): string => {
    const urlStartIndex = text.indexOf("http");
    if (urlStartIndex !== -1) {
      return text.substring(urlStartIndex).trim();
    }
    return ""; // Retorna una cadena vacía si no se encuentra una URL
  };

  const columns: GridColDef<Product>[] = [
    {
      ...GRID_CHECKBOX_SELECTION_COL_DEF,
    },
    {
      flex: 1,
      field: EFieldProductsTable.CATEGORY_IMAGE,
      headerName: "",
      minWidth: 100,
      type: "string",
      align: "center",
      headerAlign: "center",
      display: "flex",
      disableColumnMenu: true,
      headerClassName: "column-header",
      sortable: false,
      renderCell: ({ row }) => {
        const url = extractUrl(row.mainImage);
        return (
          <Image
            priority
            width={40}
            height={40}
            src={url}
            alt={url}
            style={{ width: "40px", height: "40px" }}
          />
        );
      },
    },
    {
      flex: 1,
      field: EFieldProductsTable.PRODUCT,
      headerName: "Producto",
      minWidth: 300,
      type: "string",
      align: "center",
      headerClassName: "column-header",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui text={row.name} type="columnTable" className="font-normal" />
      ),
    },

    {
      flex: 1,
      field: EFieldProductsTable.STATE,
      headerName: "Estado",
      minWidth: 280,
      type: "string",
      align: "center",
      headerAlign: "center",
      headerClassName: "column-header",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui text={row.status} type="columnTable" className="font-normal" />
      ),
    },
    {
      flex: 1,
      field: EFieldProductsTable.CATEGORY,
      headerName: "Categoría",
      minWidth: 300,
      type: "string",
      align: "center",
      headerAlign: "center",
      headerClassName: "column-header",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui text={row.type} type="columnTable" className="font-normal" />
      ),
    },
    {
      flex: 1,
      field: EFieldProductsTable.ACTIONS,
      headerName: "",
      type: "actions",
      minWidth: 45,
      sortable: false,
      disableColumnMenu: true,
      headerClassName: "column-header",
      getActions: ({ row }) => {
        return [
          <GridActionsCellItem
            key={row.id}
            icon={
              <Icon
                icon={"ic:baseline-more-vert"}
                color="#7D7D7D"
                style={{ fontSize: 16 }}
              />
            }
            label="actions"
            onClick={(e) => {
              setSelectedProduct(row.id);

              handleClick(e);
            }}
          />,
        ];
      },
    },
  ];

  return (
    <div className="flex h-full w-full flex-col rounded-[20.5px] bg-[#fff] min-w-[500px] ">
      <HeaderTable onSearchFilters={onSearchFilters} />
      <DataGrid
        loading={loading}
        checkboxSelection
        rows={products}
        columns={columns}
        disableColumnFilter
        disableRowSelectionOnClick
        hideFooterSelectedRowCount
        getRowHeight={() => "auto"}
        getRowId={(row) => row.id}
        pagination
        slots={{
          pagination: () => (
            <CustomPagination
              onPageChange={onPageChange}
              pagination={pagination}
            />
          ),
        }}
        className="account-datagrid"
        sx={{
          border: "none",
          height: "auto",
        }}
      />
      <ModalActions />
    </div>
  );
};

export default ProductsTableView;
