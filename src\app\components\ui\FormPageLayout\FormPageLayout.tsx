"use client";
import React, { useState, ReactElement, isValidElement } from "react";
import Icon from "src/app/components/ui/icon";
import TextMui from "src/app/components/ui/Text";
import ConfirmationDialog from "src/app/components/ui/toast/ConfirmationDialog";
import { useRouter } from "next/navigation";

interface FormPageLayoutProps {
  title: string;
  onDirtyChange?: (dirty: boolean) => void;
  children: ReactElement<{ onDirtyChange?: (dirty: boolean) => void }>;
  backHref: string;
}

const FormPageLayout: React.FC<FormPageLayoutProps> = ({
  title,
  onDirtyChange,
  children,
  backHref,
}) => {
  const [showDialog, setShowDialog] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const router = useRouter();

  const handleBack = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isDirty) {
      setShowDialog(true);
    } else {
      router.push(backHref);
    }
  };

  const handleDirtyChange = (dirty: boolean) => {
    setIsDirty(dirty);
    onDirtyChange?.(dirty);
  };

  return (
    <div className="flex h-full w-full p-[24px] border flex-col gap-y-[20px]">
      <header className="flex w-full justify-start items-center">
        <button
          type="button"
          onClick={handleBack}
          style={{ background: "none", border: "none", padding: 0 }}
        >
          <Icon
            icon="icon-park-outline:return"
            width="22"
            height="22"
            color="#7D7D7D"
            className="pr-5"
          />
        </button>
        <TextMui text={title} className="text-3xl text-[#333333] font-[500]" />
      </header>
      {/* El form hijo debe recibir onDirtyChange como prop */}
      {isValidElement(children)
        ? React.cloneElement(children, { onDirtyChange: handleDirtyChange })
        : children}
      <ConfirmationDialog
        show={showDialog}
        message="¿Deseas salir sin guardar los cambios?"
        onConfirm={() => {
          setShowDialog(false);
          router.push(backHref);
        }}
        onCancel={() => setShowDialog(false)}
      />
    </div>
  );
};

export default FormPageLayout;
