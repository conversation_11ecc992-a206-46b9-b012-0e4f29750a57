import { CreatePlanDto } from "src/services/produc-manager/dtos/create-plan";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useAddPlan = () => {
  const addPlan = async (params: CreatePlanDto) => {
    try {
      const response = await productManagerService.createPlan(params);
      return response;
    } catch (error) {
      console.error(error);
    }
  };

  return { addPlan };
};
