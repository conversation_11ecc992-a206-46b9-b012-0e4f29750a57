import { useForm, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  BannerFormYupSchema,
  BannerFormYupValues,
} from "./useSchemaBannerValidation";

export function useBannerForm(onSubmit: (data: BannerFormYupValues) => void) {
  const methods = useForm<BannerFormYupValues>({
    resolver: yupResolver(BannerFormYupSchema),
    defaultValues: {
      banners: [{ url_banner: "", linkUrl: "", order: 1 }],
    },
    mode: "onChange",
  });

  const { control, handleSubmit, formState } = methods;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "banners",
  });

  return {
    ...methods,
    fields,
    append,
    remove,
    handleSubmit: handleSubmit(onSubmit),
    formState,
  };
}
