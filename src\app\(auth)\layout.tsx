import Image from "next/image";
import FlowersImg from "../assets/svg/login/flowers-background.svg";
import GrassImg from "../assets/svg/login/grass.svg";
type AuthLayoutProps = {
	children: React.ReactNode;
};

const AuthLayout = ({ children }: AuthLayoutProps) => {
	return (
		<div className="flex w-screen h-screen bg-[#f5f5f5] justify-center items-center">
			<Image
				loading="lazy"
				width={356.45}
				height={490.7}
				alt=""
				src={FlowersImg}
				className="absolute top-0 right-0"
			/>
			<Image
				loading="lazy"
				width={113.49}
				height={174.93}
				alt=""
				src={GrassImg}
				className="absolute bottom-0 left-0"
			/>

			{children}
		</div>
	);
};

export default AuthLayout;
