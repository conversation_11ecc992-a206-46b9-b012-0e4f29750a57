import React, { useState } from "react";
import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";
import InputMui from "src/app/components/ui/Input";

interface HeaderTableEndorsementProps {
  onSearchFilters: (key: string, value: string | null) => void;
  activeFilters?: {
    status?: string | null;
    searchTerm?: string;
    tipoEndoso?: string | null;
  };
}

const STATUSES = ["Todos", "En revisión", "Por actualizar", "Actualizado", "Cancelado"] as const;

const HeaderTableEndorsement = ({
  onSearchFilters,
  activeFilters = {},
}: HeaderTableEndorsementProps) => {
  // ahora guardamos sólo un string
  const [order, setOrder] = useState<boolean>(true);
  const [activeStatus, setActiveStatus] = useState<string>("Todos");

  const getButtonStyle = (status: string) =>
    status === activeStatus
      ? {
          backgroundColor: "#10265F",
          color: "#ffffff",
          fontWeight: "bold",
          "&:hover": {
            backgroundColor: "#10265F",
            color: "#ffffff",
          },
          "&:focus, &:active": {
            backgroundColor: "#10265F",
            color: "#ffffff",
          },
        }
      : {};

  const handleStatusFilter = (status: string) => {
    if (status === "Todos") {
      // limpiar filtro
      setActiveStatus("Todos");
      onSearchFilters("status", null);
    } else {
      // seleccionar único estado
      setActiveStatus(status);
      onSearchFilters("status", status);
    }
  };

  const handleSearch = (searchTerm: string) => {
    onSearchFilters("searchTerm", searchTerm);
  };

  const handleOrderToggle = () => {
    const newOrder = !order;
    setOrder(newOrder);
    onSearchFilters("order", newOrder ? "DESC" : "ASC");
  };

  return (
    <div className="flex flex-col lg:flex-row w-auto h-auto p-[20px] items-center justify-between gap-y-[10px] lg:gap-y-0">
      <div className="flex items-center gap-x-[8px] flex-wrap">
        {STATUSES.map((status) => (
          <ButtonMui
            key={status}
            textbutton={status}
            variantstyle="secondary"
            sx={getButtonStyle(status)}
            onClick={() => handleStatusFilter(status)}
          />
        ))}
      </div>

      <div className="flex items-center gap-x-[10px]">
        <InputMui
          placeholder="Buscar"
          size="small"
          onChange={(e) => handleSearch(e.target.value)}
          defaultValue={activeFilters.searchTerm || ""}
        />
        <ButtonMui
          textbutton=""
          variantstyle="secondary"
          startIcon={<Icon icon={"fluent:arrow-sort-20-filled"} />}
          sx={{
            "& .MuiButton-startIcon": {
              margin: "0px",
            },
            minWidth: "auto",
          }}
          onClick={handleOrderToggle}
          title={`Ordenar ${order ? "descendente" : "ascendente"}`}
        />
      </div>
    </div>
  );
};

export default HeaderTableEndorsement;
