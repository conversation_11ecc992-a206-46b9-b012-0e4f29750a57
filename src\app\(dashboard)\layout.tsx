"use client";
import { ThemeProvider } from "@mui/material";
import React, { useEffect, useState } from "react";
import SideBar from "./components/sideBar";
import { usePathname, useRouter } from "next/navigation";
import TextMui from "../components/ui/Text";
import ButtonMui from "../components/ui/Button";
import theme from "src/themes/theme";

type DashboardLayoutProps = {
  children: React.ReactNode;
};

type Header = {
  headerName: string;
  textButtonName: string;
  route: string;
};

const Dashboardlayout = ({ children }: DashboardLayoutProps) => {
  const route = usePathname();
  const router = useRouter();

  const [header, setHeader] = useState<Header | null>(null);

  const headerConfig: { [key: string]: Header } = {
    "/dashboard": {
      headerName: "Dashboard",
      textButtonName: "",
      route: "/dashboard",
    },
    "/misc-manager": {
      headerName: "Gestor de Misceláneos",
      textButtonName: "",
      route: "/misc-manager",
    },
    "/product-manager": {
      headerName: "Gestor de Productos",
      textButtonName: "Agregar producto",
      route: "/product-manager/add-product",
    },
    "/promotion-manager": {
      headerName: "Gestor de Promociones",
      textButtonName: "Crear nueva promoción",
      route: "/promotion-manager/add-promotion",
    },
    "/endorsement-manager": {
      headerName: "Gestor de Endosos",
      textButtonName: "",
      route: "/endorsement-manager/add-endorsement",
    },
    "/claims-manager": {
      headerName: "Siniestros",
      textButtonName: "",
      route: "/claims-manager",
    },
  };

  useEffect(() => {
    setHeader(headerConfig[route] || null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [route]);

  if (
    route === headerConfig["/product-manager"]?.route ||
    route === headerConfig["/promotion-manager"]?.route ||
    route.startsWith("/product-manager/edit-product/") ||
    route.startsWith("/promotion-manager/edit-promotion/") ||
    route.startsWith("/claims-manager/details/")
  ) {
    return (
      <div className="w-full h-full bg-[#f5f5f5]">
        <main className="flex w-full flex-col items-center">
          <div className="flex w-full gap-x-[20px] max-w-[calc(1440px-3rem)]">
            {children}
          </div>
        </main>
      </div>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <div
        className={`w-screen bg-[#f5f5f5] ${
          route === "/dashboard" ? "h-auto" : "h-screen"
        }`}
      >
        <div className="flex w-auto flex-col py-[40px] px-[30px] gap-y-[25px]">
          <header className="flex w-full justify-between max-w-[100%] flex-col items-center">
            <div className="flex w-full justify-between max-w-[calc(1440px-3rem)]">
              <TextMui
                text={header?.headerName ?? ""}
                type="title"
                className="text-3xl"
              />
              {header?.textButtonName && (
                <ButtonMui
                  textbutton={header.textButtonName}
                  variant="contained"
                  onClick={() => {
                    router.push(header.route ?? "/dashboard");
                  }}
                />
              )}
            </div>
          </header>
          <main className="flex w-full flex-col items-center">
            <div className="flex w-full gap-x-[20px] max-w-[calc(1440px-3rem)]">
              <SideBar />
              {children}
            </div>
          </main>
        </div>
      </div>
    </ThemeProvider>
  );
};

export default Dashboardlayout;
