"use client";

import { NextPage } from "next";
import React from "react";

import ClaimsTableView from "./views/ClaimsTable";
import { CircularProgress } from "@mui/material";
import NoClaimsView from "./views/NoClaims";
// import { useGetClaimsPagination } from "src/hooks/claimsManager/useGetClaimsPagination";

const ClaimsManagerPage: NextPage = () => {
  //const { claims, loading } = useGetClaimsPagination();

  const claims = [1, 2, 3];
  const loading = false;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full w-[77%] flex-col max-w-[1146px]">
        <CircularProgress size={"4rem"} />
      </div>
    );
  }

  return <>{claims.length > 0 ? <ClaimsTableView /> : <NoClaimsView />}</>;
};

export default ClaimsManagerPage;
