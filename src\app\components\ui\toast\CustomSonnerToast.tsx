import { Icon } from "@iconify/react";
import { Box, IconButton, Stack, styled } from "@mui/material";
import { type ExternalToast } from "sonner";
import TextMui from "../Text";

// ? This Toast is used by src/hooks/useToast.tsx

interface ToasCardProps {
  message: string;
  title?: string;
  type: "success" | "error" | "info" | "warning" | "loading";
  iconifyIcon: string;
  isWrapText: boolean;
  isWrapTitle: boolean;
  data?: ExternalToast;
  onClose: () => void;
}

export const CustomSonnerToast = ({
  type,
  message,
  title,
  iconifyIcon,
  isWrapText,
  isWrapTitle,
  data,
  onClose,
}: ToasCardProps) => {
  const isDismissible = data
    ? typeof data.dismissible === "undefined"
      ? true
      : data.dismissible
    : true;

  return (
    <ToastCardStyled
      type={type}
      isTitle={!!title}
      isWrapText={isWrapText}
      isWrapTitle={isWrapTitle}
    >
      <Icon icon={iconifyIcon} fontSize={22} />
      <Stack direction="column" gap="6px">
        {title ? (
          <TextMui text={title} type="subtitle" style={{ color: "#fff" }} />
        ) : null}
        <TextMui text={message} type="subtitle" style={{ color: "#fff" }} />
      </Stack>
      {type !== "loading" && isDismissible ? (
        <IconButton onClick={onClose} color="inherit">
          <Icon icon="material-symbols:close" fontSize={16} />
        </IconButton>
      ) : null}
    </ToastCardStyled>
  );
};

interface ToastCardStyledProps {
  type: "success" | "error" | "info" | "warning" | "loading";
  isWrapText: boolean;
  isWrapTitle: boolean;
  isTitle?: boolean;
}

const ToastCardStyled = styled(Box, {
  shouldForwardProp: (prop) =>
    !["type", "isWrapText", "isWrapTitle", "isTitle"].includes(prop as string),
})<ToastCardStyledProps>(
  ({ theme, type, isWrapText, isWrapTitle, isTitle }) => ({
    display: "flex",
    alignItems: isTitle ? "flex-start" : "center",
    gap: "12px",

    ...(type === "success"
      ? {
          backgroundColor: theme.palette.success.main,
          color: theme.palette.success.main,
        }
      : type === "error"
      ? {
          backgroundColor: theme.palette.error.main,
          color: "#fff",
        }
      : type === "info"
      ? {
          backgroundColor: theme.palette.info.main,
          color: theme.palette.info.main,
        }
      : type === "warning"
      ? {
          backgroundColor: theme.palette.warning.main,
          color: theme.palette.warning.main,
        }
      : type === "loading"
      ? {
          backgroundColor: theme.palette.info.main,
          color: theme.palette.info.main,
        }
      : {}),

    borderRadius: "8px",
    padding: "6px 32px 6px 16px",
    minHeight: "54px",
    position: "relative",

    "& > svg": {
      flexShrink: 0,
      marginTop: isTitle ? "16px" : "0",
    },

    ...(isTitle
      ? {
          "& > div > span:nth-of-type(1)": {
            [theme.breakpoints.up("md")]: {
              whiteSpace: isWrapTitle ? "normal" : "nowrap",
            },
          },
        }
      : {}),

    "& > div > span:nth-of-type(2)": {
      [theme.breakpoints.up("md")]: {
        whiteSpace: isWrapText ? "normal" : "nowrap",
      },
    },

    "& > button": {
      position: "absolute",
      right: "2px",
      top: "2px",
    },
  })
);
