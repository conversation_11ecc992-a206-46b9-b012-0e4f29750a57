import { ENDORSEMENT_MANAGER_ROUTES } from "src/configs/api-routes";
import { AppAlpexApiGateWay } from "../app.equinox.api-getway";
import { GetAllOnReviewResponseDto } from "./dtos/get-all-on-review.dto";
import { 
  SearchEndorsementsDto, 
  SearchEndorsementsResponseDto 
} from "./dtos/search-endorsements.dto";

class EndorsementManagerService {
  /**
   * Obtiene el conteo de endosos en revisión para el badge
   */
  public async getAllOnReview(): Promise<GetAllOnReviewResponseDto> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get<GetAllOnReviewResponseDto>(
        ENDORSEMENT_MANAGER_ROUTES.GET_ALL_ON_REVIEW
      );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  /**
   * Busca endosos por nombre de usuario, ID o estatus
   */
  public async searchEndorsements(params: SearchEndorsementsDto): Promise<SearchEndorsementsResponseDto> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get<SearchEndorsementsResponseDto>(
        ENDORSEMENT_MANAGER_ROUTES.SEARCH_ENDORSEMENTS,
        { params }
      );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }
}

const endorsementManagerService = new EndorsementManagerService();
export default endorsementManagerService;
