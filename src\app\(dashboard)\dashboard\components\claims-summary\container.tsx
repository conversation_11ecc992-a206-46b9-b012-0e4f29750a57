import React from "react";
import ClaimCard from "./card";
import AddBoxOutlinedIcon from "@mui/icons-material/AddBoxOutlined";
import AccessTimeOutlinedIcon from "@mui/icons-material/AccessTimeOutlined";
import ClaimsTablesContainer from "./table/container";

const ClaimsSummary = () => (
  <section className="w-full max-w-5xl mx-auto flex flex-col items-center">
    {/* Cards */}
    <div className="flex gap-6 mb-8 w-full">
      <div className="flex-1">
        <ClaimCard icon={<AddBoxOutlinedIcon />} title="Nuevas" value={45} />
      </div>
      <div className="flex-1">
        <ClaimCard
          icon={<AccessTimeOutlinedIcon />}
          title="Pendientes"
          value={1500}
        />
      </div>
    </div>
    {/* Table */}
    <ClaimsTablesContainer />
  </section>
);

export default ClaimsSummary;
