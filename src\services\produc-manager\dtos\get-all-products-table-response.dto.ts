import { ProductManagerTablePaginationDto } from "./get-all-products-table.dto";

export interface Product {
  id: number;
  name: string;
  idZurich: string;
  type: string;
  generalCoverage: string;
  subtitle: string;
  status: string;
  additionalDetails: string;
  mainImage: string;
  secondaryImage: string;
  coverageName: string;
  coverageDescription: string;
  coberturas: ProductCoverage[];
  planesProductos: ProductInsuredAmount[];
}

export type ProductCoverage = {
  id: number;
  name: string;
  description: string;
  percentageInsuredAmount: string;
};

export type ProductInsuredAmount = {
  id: number;
  insuredAmount: string;
  netPremium: string;
  deductible: string;
  iva: string;
  total: string;
};
export interface ProductManagerTableResponseDto {
  data: Product[];
  pagination: ProductManagerTablePaginationDto;
}
