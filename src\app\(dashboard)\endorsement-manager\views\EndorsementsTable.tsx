"use client";

import React, { useState, useMemo, useEffect } from "react";
import { DataGrid, GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { useRouter } from "next/navigation";
import { Menu, MenuItem } from "@mui/material";

import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";

import { Endorsement } from "../types/endorsement.types";
import { useSearchEndorsements } from "src/hooks/endorsementManager/useSearchEndorsements";
import {
  adaptEndorsementSearchResults,
  adaptFiltersToApiParams
} from "../utils/endorsement-adapter";
import HeaderTableEndorsement from "../components/Table/components/HeaderTableEndorsement";
import ColumnHeader from "../components/Table/components/ColumnHeader";

const EndorsementsTableView = () => {
  const router = useRouter();
  const [filters, setFilters] = useState({
    status: null as string | null,
    searchTerm: "",
    order: "DESC" as "ASC" | "DESC",
    tipoEndoso: null as string | null,
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedEndorsement, setSelectedEndorsement] =
    useState<Endorsement | null>(null);

  // Estado de paginación
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 9,
  });

  // Hook para búsqueda de endosos
  const { endorsements: searchResults, loading, searchEndorsements } = useSearchEndorsements();

  // Convertir resultados de la API al formato de la tabla
  const filteredEndorsements = useMemo(() => {
    return adaptEndorsementSearchResults(searchResults);
  }, [searchResults]);

  // Cargar datos iniciales
  useEffect(() => {
    const loadInitialData = async () => {
      const apiParams = adaptFiltersToApiParams(filters);
      await searchEndorsements({
        ...apiParams,
        page: 1,
        limit: paginationModel.pageSize,
      });
    };

    loadInitialData();
  }, []); // Solo ejecutar al montar el componente

  const handleSearchFilters = async (
    key: string,
    value: string | string[] | null
  ) => {
    const newFilters = {
      ...filters,
      [key]: value,
    };

    setFilters(newFilters);

    // Realizar búsqueda con los nuevos filtros
    const apiParams = adaptFiltersToApiParams(newFilters);
    await searchEndorsements({
      ...apiParams,
      page: paginationModel.page + 1, // API usa páginas basadas en 1
      limit: paginationModel.pageSize,
    });
  };

  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    endorsement: Endorsement
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedEndorsement(endorsement);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedEndorsement(null);
  };

  const handleEndorsementClick = (endorsementId: string) => {
    // Navegar al detalle del endoso
    router.push(`/endorsement-manager/detail/${endorsementId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "En revisión":
        return "#FF9800"; // Naranja
      case "Por actualizar":
        return "#F44336"; // Rojo
      case "Actualizado":
        return "#4CAF50"; // Verde
      case "Cancelado":
        return "#9E9E9E"; // Gris
      default:
        return "#000000";
    }
  };

  const columns: GridColDef[] = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.8,
      minWidth: 80,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: "#1976d2",
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textDecoration: "underline",
              cursor: "pointer",
            }}
            onClick={() => handleEndorsementClick(params.value)}
            onMouseEnter={(e) =>
              ((e.target as HTMLElement).style.color = "#1565c0")
            }
            onMouseLeave={(e) =>
              ((e.target as HTMLElement).style.color = "#1976d2")
            }
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "usuario",
      headerName: "Usuario",
      flex: 1.2,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: "#7D7D7D",
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textAlign: "center",
            }}
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "tipoEndoso",
      headerName: "Tipo de endoso",
      flex: 1.8,
      minWidth: 160,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: "#7D7D7D",
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textAlign: "center",
            }}
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "productoAsegurado",
      headerName: "Producto",
      flex: 1.2,
      minWidth: 100,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: "#7D7D7D",
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textAlign: "center",
            }}
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "fechaRegistro",
      headerName: "Fecha",
      flex: 1,
      minWidth: 90,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: "#7D7D7D",
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textAlign: "center",
            }}
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "estatus",
      headerName: "Estatus",
      flex: 1.4,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <span
            style={{
              color: getStatusColor(params.value),
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 400,
              textAlign: "center",
            }}
          >
            {params.value || ""}
          </span>
        </div>
      ),
    },
    {
      field: "acciones",
      headerName: "Acciones",
      flex: 1,
      minWidth: 90,
      sortable: false,
      filterable: false,
      renderHeader: (params) => <ColumnHeader colDef={params.colDef} />,
      renderCell: (params: GridRenderCellParams<Endorsement>) => (
        <div className="flex items-center justify-center h-full w-full">
          <ButtonMui
            textbutton=""
            variantstyle="secondary"
            startIcon={<Icon icon="tabler:dots" />}
            sx={{
              "& .MuiButton-startIcon": {
                margin: "0px",
              },
              minWidth: "auto",
              border: "none",
              backgroundColor: "transparent",
              "&:hover": {
                backgroundColor: "transparent",
                border: "none",
              },
              "&:focus": {
                backgroundColor: "transparent",
                border: "none",
              },
            }}
            onClick={(event) => handleMenuClick(event, params.row)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="flex h-full w-full flex-col rounded-[20.5px] bg-[#fff] min-w-[500px]">
      <HeaderTableEndorsement
        onSearchFilters={handleSearchFilters}
        activeFilters={filters}
      />

      <DataGrid
        loading={loading}
        rows={filteredEndorsements}
        columns={columns}
        disableColumnFilter
        disableColumnMenu
        disableColumnSelector
        disableRowSelectionOnClick
        hideFooterSelectedRowCount
        getRowHeight={() => "auto"}
        getRowId={(row) => row.id}
        pagination
        paginationModel={paginationModel}
        onPaginationModelChange={async (newModel) => {
          setPaginationModel(newModel);
          // Realizar nueva búsqueda con la nueva página
          const apiParams = adaptFiltersToApiParams(filters);
          await searchEndorsements({
            ...apiParams,
            page: newModel.page + 1, // API usa páginas basadas en 1
            limit: newModel.pageSize,
          });
        }}
        pageSizeOptions={[9]}
        paginationMode="client"
        slots={{
          pagination: () => {
            const totalPages = Math.ceil(filteredEndorsements.length / paginationModel.pageSize);
            const currentPage = paginationModel.page + 1;

            const handlePrevious = () => {
              if (paginationModel.page > 0) {
                setPaginationModel(prev => ({ ...prev, page: prev.page - 1 }));
              }
            };

            const handleNext = () => {
              if (paginationModel.page < totalPages - 1) {
                setPaginationModel(prev => ({ ...prev, page: prev.page + 1 }));
              }
            };

            return (
              <div className="flex justify-center items-center p-4">
                <div className="flex items-center gap-3">
                  <button
                    onClick={handlePrevious}
                    disabled={paginationModel.page === 0}
                    className="w-10 h-10 rounded-lg bg-blue-900 text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-800 transition-colors"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>

                  <span
                    className="text-gray-600 font-medium min-w-[40px] text-center"
                    style={{
                      fontFamily: "Poppins",
                      fontSize: "16px",
                      fontWeight: 400,
                    }}
                  >
                    {currentPage}/{totalPages}
                  </span>

                  <button
                    onClick={handleNext}
                    disabled={paginationModel.page >= totalPages - 1}
                    className="w-10 h-10 rounded-lg bg-blue-900 text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-800 transition-colors"
                    style={{ backgroundColor: "#10265F" }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            );
          },
        }}
        className="account-datagrid"
        sx={{
          border: "none",
          height: "auto",
          width: "100%",
          "& .MuiDataGrid-columnHeaders": {
            background: "rgba(87, 90, 111, 0.12)",
            borderBottom: "none",
          },
          "& .MuiDataGrid-columnHeader": {
            background: "rgba(87, 90, 111, 0.12)",
            display: "flex !important",
            alignItems: "center !important",
            justifyContent: "center !important",
            textAlign: "center !important",
            padding: "0 !important",
            "&:not(:last-of-type)": {
              borderRight: "1px solid rgba(87, 90, 111, 0.12)",
            },
            "&:focus": {
              outline: "none",
            },
            "&:focus-within": {
              outline: "none",
            },
            "& .MuiDataGrid-columnSeparator": {
              display: "none",
            },
            "& .MuiDataGrid-iconButtonContainer": {
              display: "none",
            },
            "& .MuiDataGrid-sortIcon": {
              display: "none",
            },
            "& .MuiDataGrid-menuIcon": {
              display: "none",
            },
          },
          "& .MuiDataGrid-columnHeaderTitle": {
            display: "none !important",
          },
          "& .MuiDataGrid-columnHeaderTitleContainer": {
            display: "flex !important",
            alignItems: "center !important",
            justifyContent: "center !important",
            width: "100% !important",
            height: "100% !important",
            padding: "0 !important",
          },
          "& .MuiDataGrid-cell": {
            borderBottom: "1px solid #e0e0e0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "8px 0 !important",
          },
          "& .MuiDataGrid-root": {
            width: "100%",
          },
          "& .MuiDataGrid-footerContainer": {
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            padding: "16px",
            borderTop: "1px solid #e0e0e0",
          },
          "& .MuiTablePagination-root": {
            display: "flex",
            justifyContent: "center",
            width: "100%",
          },
          "& .MuiTablePagination-toolbar": {
            justifyContent: "center",
          },
        }}
      />

      {/* Menú de acciones */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            if (selectedEndorsement) {
              handleEndorsementClick(selectedEndorsement.id);
            }
            handleMenuClose();
          }}
        >
          <Icon icon="tabler:eye" className="mr-2" />
          Ver detalle
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Icon icon="tabler:edit" className="mr-2" />
          Editar
        </MenuItem>
      </Menu>
    </div>
  );
};

export default EndorsementsTableView;
