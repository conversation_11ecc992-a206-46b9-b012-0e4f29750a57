"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import TextMui from "src/app/components/ui/Text";
import ColumnHeader from "../components/Table/components/ColumnHeader";
import HeaderTable from "../components/Table/components/HeaderTable";

enum EFieldClaimsTable {
  ID = "id",
  USER = "user",
  PRODUCT = "product",
  COVERAGE = "coverage",
  DATE = "date",
  STATUS = "status",
  AMOUNT = "amount",
}

interface Claim {
  id: number;
  user: string;
  product: string;
  coverage: string;
  date: string;
  status: string;
  amount: number;
}

const ClaimsTableView = () => {
  const router = useRouter();

  const [claims] = useState<Claim[]>([
    {
      id: 1,
      user: "Usuario 1",
      product: "Rodada",
      coverage: "Cobertura",
      date: "01/01",
      status: "Documentos",
      amount: 1500,
    },
    {
      id: 2,
      user: "Usuario 2",
      product: "Rodada",
      coverage: "Cobertura",
      date: "02/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
    {
      id: 3,
      user: "Usuario 3",
      product: "Rodada",
      coverage: "Cobertura",
      date: "03/01",
      status: "Proceso de pago",
      amount: 1500,
    },
    {
      id: 4,
      user: "Usuario 4",
      product: "Rodada",
      coverage: "Cobertura",
      date: "04/01",
      status: "Pagado",
      amount: 1500,
    },
    {
      id: 5,
      user: "Usuario 5",
      product: "Rodada",
      coverage: "Cobertura",
      date: "05/01",
      status: "Documentos",
      amount: 1500,
    },
    {
      id: 6,
      user: "Usuario 6",
      product: "Rodada",
      coverage: "Cobertura",
      date: "06/01",
      status: "Proceso de pago",
      amount: 1500,
    },
    {
      id: 7,
      user: "Usuario 7",
      product: "Rodada",
      coverage: "Cobertura",
      date: "07/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
    {
      id: 8,
      user: "Usuario 8",
      product: "Rodada",
      coverage: "Cobertura",
      date: "08/01",
      status: "Documentos",
      amount: 1500,
    },
    {
      id: 9,
      user: "Usuario 9",
      product: "Rodada",
      coverage: "Cobertura",
      date: "09/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
    {
      id: 10,
      user: "Usuario 10",
      product: "Rodada",
      coverage: "Cobertura",
      date: "09/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
    {
      id: 11,
      user: "Usuario 11",
      product: "Rodada",
      coverage: "Cobertura",
      date: "09/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
    {
      id: 12,
      user: "Usuario 12",
      product: "Rodada",
      coverage: "Cobertura",
      date: "09/01",
      status: "Propuesta finiquito",
      amount: 1500,
    },
  ]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>(claims);
  const [originalClaims] = useState<Claim[]>(claims);
  const [loading] = useState<boolean>(false);

  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 9,
  });

  const onSearchFilters = (key: string, value: string | string[] | null) => {
    switch (key) {
      case "status":
        if (!value) {
          setFilteredClaims(originalClaims);
        } else {
          const statusArray = value.toString().split(",");
          const filtered = originalClaims.filter((claim) =>
            statusArray.includes(claim.status)
          );
          setFilteredClaims(filtered);
        }
        break;
      case "searchTerm":
        const searchTerm = (value as string).toLowerCase();
        const searchFiltered = originalClaims.filter((claim) =>
          Object.values(claim).some((val) =>
            val.toString().toLowerCase().includes(searchTerm)
          )
        );
        setFilteredClaims(searchFiltered);
        break;
      case "order":
        const ordered = [...filteredClaims].sort((a, b) => {
          if (value === "ASC") return a.date.localeCompare(b.date);
          return b.date.localeCompare(a.date);
        });
        setFilteredClaims(ordered);
        break;
      default:
        break;
    }
  };

  const columns: GridColDef<Claim>[] = [
    {
      flex: 1,
      field: EFieldClaimsTable.ID,
      headerName: "ID",
      minWidth: 100,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui
            text={`${row.id}`}
            type="columnTable"
            className="font-bold text-[#10265F] underline cursor-pointer"
            onClick={() => router.push(`/claims-manager/details/${row.id}`)}
          />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.USER,
      headerName: "Usuario",
      minWidth: 200,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui text={row.user} type="columnTable" className="font-normal" />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.PRODUCT,
      headerName: "Producto",
      minWidth: 200,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui
            text={row.product}
            type="columnTable"
            className="font-normal"
          />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.COVERAGE,
      headerName: "Cobertura",
      minWidth: 200,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui
            text={row.coverage}
            type="columnTable"
            className="font-normal"
          />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.DATE,
      headerName: "Fecha",
      minWidth: 150,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui text={row.date} type="columnTable" className="font-normal" />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.STATUS,
      headerName: "Estatus",
      minWidth: 160,
      type: "string",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui
            text={row.status}
            type="columnTable"
            className="font-normal"
          />
        </div>
      ),
    },
    {
      flex: 1,
      field: EFieldClaimsTable.AMOUNT,
      headerName: "Monto reclamado",
      minWidth: 150,
      type: "number",
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <div className="flex items-center justify-center w-full h-full">
          <TextMui
            text={`$${row.amount.toLocaleString("es-MX")}`}
            type="columnTable"
            className="font-normal"
          />
        </div>
      ),
    },
  ];

  return (
    <div className="flex h-full w-full flex-col rounded-[20.5px] bg-[#fff] min-w-[500px]">
      <HeaderTable onSearchFilters={onSearchFilters} />
      <DataGrid
        loading={loading}
        rows={filteredClaims}
        columns={columns}
        disableColumnFilter
        disableRowSelectionOnClick
        hideFooterSelectedRowCount
        getRowHeight={() => "auto"}
        getRowId={(row) => row.id}
        pagination
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        pageSizeOptions={[9]}
        paginationMode="client"
        // slots={{
        //   pagination: () => (
        //     <CustomPagination
        //       onPageChange={onPageChange}
        //       pagination={pagination}
        //     />
        //   ),
        // }}
        slots={{
          pagination: () => {
            const totalPages = Math.ceil(
              filteredClaims.length / paginationModel.pageSize
            );
            const currentPage = paginationModel.page + 1;

            const handlePrevious = () => {
              if (paginationModel.page > 0) {
                setPaginationModel((prev) => ({
                  ...prev,
                  page: prev.page - 1,
                }));
              }
            };

            const handleNext = () => {
              if (paginationModel.page < totalPages - 1) {
                setPaginationModel((prev) => ({
                  ...prev,
                  page: prev.page + 1,
                }));
              }
            };

            return (
              <div className="flex justify-center items-center p-4">
                <div className="flex items-center gap-3">
                  <button
                    onClick={handlePrevious}
                    disabled={paginationModel.page === 0}
                    className="w-10 h-10 rounded-lg bg-blue-900 text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-800 transition-colors"
                    style={{ backgroundColor: "#10265F" }}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M15 18L9 12L15 6"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>

                  <span
                    className="text-gray-600 font-medium min-w-[40px] text-center"
                    style={{
                      fontFamily: "Poppins",
                      fontSize: "16px",
                      fontWeight: 400,
                    }}
                  >
                    {currentPage}/{totalPages}
                  </span>

                  <button
                    onClick={handleNext}
                    disabled={paginationModel.page >= totalPages - 1}
                    className="w-10 h-10 rounded-lg bg-blue-900 text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-800 transition-colors"
                    style={{ backgroundColor: "#10265F" }}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            );
          },
        }}
        className="account-datagrid"
        sx={{
          border: "none",
          height: "auto",
          "& .MuiDataGrid-columnHeaders": {
            background: "rgba(87, 90, 111, 0.12)",
            borderBottom: "none",
          },
          "& .MuiDataGrid-cell": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "14px 0",
          },
          "& .MuiDataGrid-footerContainer": {
            justifyContent: "center",
            borderTop: "none",
          },
          "& .MuiDataGrid-pagination": {
            justifyContent: "center",
          },
        }}
      />
    </div>
  );
};

export default ClaimsTableView;
