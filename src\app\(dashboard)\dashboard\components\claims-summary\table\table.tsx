import React from "react";

interface Column {
  header: string;
  accessor: string;
  align?: "left" | "right" | "center";
}

interface ClaimsTableProps {
  columns: Column[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Record<string, any>[];
}

export default function ClaimsTable({ columns, data }: ClaimsTableProps) {
  return (
    <div className="w-full bg-white rounded-2xl shadow-xl overflow-hidden">
      <table className="w-full text-sm text-left text-[#6D6D6D]">
        <thead className="bg-[#f0f0f0] text-black">
          <tr>
            {columns.map((col) => (
              <th
                key={col.accessor}
                className={`px-4 py-3 font-normal ${
                  col.align === "right"
                    ? "text-right"
                    : col.align === "center"
                    ? "text-center"
                    : "text-left"
                }`}
              >
                {col.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, idx) => (
            <tr key={idx} className="">
              {/* <tr key={idx} className="border-t"></tr> */}
              {columns.map((col) => (
                <td
                  key={col.accessor}
                  className={`px-4 py-2 ${
                    col.align === "right"
                      ? "text-right"
                      : col.align === "center"
                      ? "text-center"
                      : "text-left"
                  }`}
                >
                  {item[col.accessor]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
