import { useFieldArray, useForm } from "react-hook-form";

import useSaveForm from "./useSaveForm";
import { yupResolver } from "@hookform/resolvers/yup";
import { useSchemaValidation } from "./useSchemaValidation";
import { AddProductForm } from "../components/Form/interfaces/form.interface";
import { initialValues } from "../components/Form/types/product-form";

export const useAddProductForm = () => {
	const { schema } = useSchemaValidation();
	const {
		control,
		setValue,
		handleSubmit,
		setError,
		watch,
		clearErrors,
		resetField,
		reset,
		formState: { errors, isDirty, isValid },
	} = useForm<AddProductForm>({
		mode: "onChange",
		resolver: yupResolver(schema),
		defaultValues: initialValues,
	});

	const arrayFieldCoverage = useFieldArray<AddProductForm>({
		name: "coberturas",
		control,
	});
	const arrayFieldPlans = useFieldArray<AddProductForm>({
		name: "planesProductos",
		control,
	});

	const { onSubmit } = useSaveForm();

	const handleClickSave = () => {
		handleSubmit(onSubmit)();
		// handleSubmit(onSubmit)();
	};

	return {
		hookForm: {
			control,
			errors,
			isValid,
			setValue,
			isDirty,
			setError,
			watch,
			clearErrors,
			reset,
			resetField,
		},
		handleSubmit,
		onSubmit: handleSubmit(onSubmit),
		handleClickSave,
		arrayFieldCoverage,
		arrayFieldPlans,
	};
};
