"use client";
import React, { forwardRef, useEffect, useLayoutEffect, useRef } from "react";
import Quill, { Delta } from "quill";
import DeltaStatic from "quill";
import Sources from "quill";

import "quill/dist/quill.snow.css";
import "../QuillEditor/quill-editor.css";

interface IEditorProps {
	readOnly: boolean;
	defaultValue: Delta;
	descriptionProduct?: string;
	onTextChange: (
		delta: DeltaStatic,
		oldDelta: DeltaStatic,
		source: Sources
	) => void;
}

const toolbarOptions = [
	["bold", "italic", "underline"], // toggled buttons
	["link"],
	[{ align: [] }],
	[{ list: "ordered" }, { list: "bullet" }],
];

const Editor = forwardRef<Quill | null, IEditorProps>(
	({ readOnly, defaultValue, onTextChange, descriptionProduct }, ref) => {
		const containerRef = useRef<HTMLDivElement | null>(null);
		const defaultValueRef = useRef(defaultValue);
		const onTextChangeRef = useRef(onTextChange);
		const quillRef = useRef<Quill | null>(null);

		useLayoutEffect(() => {
			onTextChangeRef.current = onTextChange;
		});

		useEffect(() => {
			if (ref && typeof ref !== "function" && ref.current) {
				ref.current.enable(!readOnly);
			}
		}, [ref, readOnly]);

		useEffect(() => {
			const container = containerRef.current;
			if (!container) return;

			const editorContainer = container.appendChild(
				container.ownerDocument.createElement("div")
			);

			const quill = new Quill(editorContainer, {
				theme: "snow",
				placeholder: "Descripción",
				modules: {
					toolbar: toolbarOptions,
				},
			});

			quillRef.current = quill;

			if (typeof ref === "function") {
				ref(quill);
			} else if (ref) {
				ref.current = quill;
			}

			// Establecer contenido inicial desde defaultValue
			if (defaultValueRef.current) {
				quill.setContents(defaultValueRef.current as Delta);
			}

			// Establecer contenido desde la API (descriptionProduct)
			if (descriptionProduct) {
				quill.clipboard.dangerouslyPasteHTML(descriptionProduct);
			}

			quill.on(
				Quill.events.TEXT_CHANGE,
				(delta: DeltaStatic, oldDelta: DeltaStatic, source: Sources) => {
					onTextChangeRef.current?.(delta, oldDelta, source);
				}
			);

			return () => {
				if (typeof ref === "function") {
					ref(null);
				} else if (ref) {
					ref.current = null;
				}
				container.innerHTML = "";
			};
		}, [ref, descriptionProduct]);

		// Actualizar el contenido del editor cuando cambie descriptionProduct
		useEffect(() => {
			if (quillRef.current && descriptionProduct) {
				quillRef.current.clipboard.dangerouslyPasteHTML(descriptionProduct);
				// Mover el cursor al final del texto
				const length = quillRef.current.getLength(); // Obtiene la longitud del contenido
				quillRef.current.setSelection(length - 1, 0); // Mueve el cursor al final
			}
		}, [descriptionProduct]);

		return (
			<div ref={containerRef} className="flex w-full flex-col min-h-[120px]" />
		);
	}
);

Editor.displayName = "Editor";

export default Editor;
