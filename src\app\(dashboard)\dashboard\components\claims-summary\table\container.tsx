import React from "react";
import ClaimsTable from "./table";

const ClaimsTablesContainer = () => (
  <div className="w-full flex gap-8">
    <div className="w-1/2 h-auto">
      <h3 className="mb-4 font-medium text-xl">Reclamaciones por producto</h3>
      <ClaimsTable
        columns={[
          { header: "Producto", accessor: "producto" },
          {
            header: "Reclamaciones",
            accessor: "reclamaciones",
            align: "right",
          },
        ]}
        data={[
          { producto: "Asalto", reclamaciones: 495 },
          { producto: "Phishing", reclamaciones: 1893 },
          { producto: "Hackeo", reclamaciones: 895 },
          { producto: "Rodada", reclamaciones: 95 },
        ]}
      />
    </div>
    <div className="w-1/2 h-auto">
      <h3 className="mb-4 font-medium text-xl">Reclamaciones por estatus</h3>
      <ClaimsTable
        columns={[
          { header: "Estatus", accessor: "estatus" },
          {
            header: "Reclamaciones",
            accessor: "reclamaciones",
            align: "right",
          },
        ]}
        data={[
          { estatus: "Documentos", reclamaciones: 95 },
          { estatus: "Propuestas de finiquito", reclamaciones: 13 },
          { estatus: "Proceso de pago", reclamaciones: 55 },
          { estatus: "Pagado", reclamaciones: 74 },
        ]}
      />
    </div>
  </div>
);

export default ClaimsTablesContainer;
