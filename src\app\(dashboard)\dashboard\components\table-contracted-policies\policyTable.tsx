"use client";

import { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import DownloadIcon from "@mui/icons-material/Download";
import FilterListIcon from "@mui/icons-material/FilterList";

const policies = [
  {
    id: "P-0001",
    client: "<PERSON>",
    product: "Asalto",
    date: "25/03/2025",
    amount: "$1,200",
    status: "Vigente",
  },
  {
    id: "P-0002",
    client: "Carlos Ríos",
    product: "Hackeo",
    date: "22/03/2025",
    amount: "$1,800",
    status: "Por renovar",
  },
  {
    id: "P-0003",
    client: "<PERSON> Mu<PERSON>",
    product: "Rodada",
    date: "20/03/2025",
    amount: "$900",
    status: "Cancelada",
  },
  {
    id: "P-0004",
    client: "<PERSON>",
    product: "Rodada",
    date: "18/03/2025",
    amount: "$2,500",
    status: "Vigente",
  },
  {
    id: "P-0005",
    client: "<PERSON>",
    product: "Phishing",
    date: "15/03/2025",
    amount: "$1,100",
    status: "Pagada",
  },
];

export default function PolicyTable() {
  const [search, setSearch] = useState("");

  const filtered = policies.filter((p) =>
    Object.values(p).some((val) =>
      val.toLowerCase().includes(search.toLowerCase())
    )
  );

  return (
    <section className="p-6 max-w-6xl mx-auto">
      <div className="flex items-center justify-between gap-4 mb-4 w-full">
        <div className="flex items-center border rounded-lg px-3 py-3 w-full max-w-[255px]">
          <input
            type="text"
            placeholder="Buscar"
            className="ml-2 outline-none w-full"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <SearchIcon className="w-5 h-5 text-gray-500 ml-2" />
        </div>

        <div>
          <button className="bg-[#10265F] text-white px-10 py-3 rounded-lg flex items-center gap-2 font-normal text-sm">
            <DownloadIcon className="w-4 h-4" />
            Descargar tabla
          </button>
        </div>
      </div>

      {/* Tabla */}
      <div className="overflow-x-auto bg-white shadow-md rounded-lg">
        <table className="min-w-full text-left text-sm text-[#6D6D6D]">
          <thead className="bg-[#f0f0f0] text-black">
            <tr>
              {[
                "Póliza",
                "Cliente",
                "Producto",
                "Fecha de contratación",
                "Monto",
                "Estatus",
              ].map((head, i) => (
                <th
                  key={i}
                  className={`px-4 py-3 whitespace-nowrap font-normal ${
                    head === "Fecha de contratación" || head === "Monto"
                      ? "text-center"
                      : "text-left"
                  }`}
                >
                  <div
                    className={`gap-1 ${
                      head === "Fecha de contratación"
                        ? "flex justify-center items-center"
                        : "flex items-center"
                    }`}
                  >
                    {head}
                    {["Póliza", "Cliente", "Monto"].includes(head) ? (
                      <FilterListIcon className="w-4 h-4 text-gray-400" />
                    ) : null}
                    {["Fecha de contratación", "Estatus"].includes(head) ? (
                      <SearchIcon className="w-4 h-4 text-gray-400" />
                    ) : null}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filtered.map((p, idx) => (
              <tr key={idx} className="">
                <td className="px-4 py-3">{p.id}</td>
                <td className="px-4 py-3">{p.client}</td>
                <td className="px-4 py-3">{p.product}</td>
                <td className="px-4 py-3 text-center">{p.date}</td>
                <td className="px-4 py-3">{p.amount}</td>
                <td className="px-4 py-3">{p.status}</td>
              </tr>
            ))}
            {filtered.length === 0 && (
              <tr>
                <td colSpan={6} className="text-center py-6 text-gray-500">
                  No se encontraron pólizas.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </section>
  );
}
