import { useEffect, useState } from "react";
import { Category } from "src/services/produc-manager/dtos/get-categories";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useGetCategories = () => {
  const [categories, setCategories] = useState<Category[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await productManagerService.getCategories();
        setCategories(response);
      } catch (err) {
        console.error("Error al obtener las categorías:", err);
        setError("No se pudieron cargar las categorías.");
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, loading, error, setCategories };
};

// import { useEffect, useState } from "react";
// import { Category } from "src/services/produc-manager/dtos/get-categories";
// import productManagerService from "src/services/produc-manager/product-manager.service";

// export const useGetCategories = () => {
//   const [categories, setCategories] = useState<{ id: string; name: string }[] | null>(null);
//   const [loading, setLoading] = useState<boolean>(true);
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     const fetchCategories = async () => {
//       try {
//         setLoading(true);
//         const response = await productManagerService.getCategories();
//         // Transforma las categorías para que usen `name` en lugar de `type`
//         const transformedCategories = response.map((category: { id: number; type: string }) => ({
//           id: String(category.id),
//           name: category.type,
//         }));
//         setCategories(transformedCategories);
//       } catch (err) {
//         console.error("Error al obtener las categorías:", err);
//         setError("No se pudieron cargar las categorías.");
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchCategories();
//   }, []);

//   return { categories, loading, error };
// };