import { EditProductForm } from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useUpdateProduct = () => {
	const updateProduct = async (
		params: EditProductForm,
		idProduct: string | string[]
	) => {
		try {
			const response = await productManagerService.updateProduct(
				params,
				idProduct
			);
			return response;
		} catch (error) {
			console.log("Error", error);
			throw error;
		}
	};

	return { updateProduct };
};
