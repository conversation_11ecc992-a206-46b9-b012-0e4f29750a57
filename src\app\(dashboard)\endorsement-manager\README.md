# Endorsement Manager - Gestor de Endosos

## Descripción
Módulo para la gestión de historial de endosos con funcionalidades de filtrado, búsqueda y visualización de datos.

## Estructura del Proyecto

```
endorsement-manager/
├── components/
│   └── Table/
│       └── components/
│           ├── HeaderTableEndorsement.tsx  # Filtros y buscador
│           └── ColumnHeader.tsx            # Encabezados de columnas
├── data/
│   └── dummy-endorsements.ts              # Datos de prueba
├── detail/
│   └── [endorsementId]/
│       └── page.tsx                       # Página de detalle
├── types/
│   └── endorsement.types.ts               # Tipos TypeScript
├── views/
│   └── EndorsementsTable.tsx              # Vista principal de la tabla
├── page.tsx                               # Página principal
└── README.md                              # Documentación
```

## Funcionalidades Implementadas

### ✅ Filtros por Estatus
- **Todos**: Muestra todos los registros
- **En revisión**: Filtro por estatus específico
- **Por actualizar**: Filtro por estatus específico
- **Actualizado**: Filtro por estatus específico
- **Cancelado**: Filtro por estatus específico

**Características:**
- Filtros múltiples (se pueden combinar)
- Estilo visual para filtros activos
- Botón "Todos" limpia todos los filtros

### ✅ Buscador
- Búsqueda por ID de endoso
- Búsqueda por nombre de usuario
- Búsqueda en tiempo real

### ✅ Filtro por Tipo de Endoso
- Menú desplegable con radio buttons
- Opciones: Cambio de domicilio, Cancelación, Cambio de nombre
- Ubicado entre el buscador y el botón de ordenamiento

### ✅ Ordenamiento
- Ordenamiento por fecha de registro
- Alternancia entre ASC/DESC
- Icono visual para indicar dirección

### ✅ Tabla Interactiva
**Columnas:**
- **ID Endoso**: Clicable, navega al detalle
- **Usuario**: Nombre del usuario
- **Tipo de Endoso**: Tipo de solicitud
- **Producto**: Producto asegurado
- **Fecha**: Fecha de registro
- **Estatus**: Estado actual con colores
- **Acciones**: Menú de opciones

### ✅ Navegación
- Click en ID navega a `/endorsement-manager/detail/[id]`
- Página de detalle básica implementada
- Botón "Ir a Inicio" para regresar

## Datos Dummy

Los datos de prueba están en `data/dummy-endorsements.ts` y incluyen:
- registros que coinciden con el diseño de Figma
- Diferentes tipos de endoso
- Todos los estatus posibles
- Fechas secuenciales para testing

## Tipos de Datos

### EndorsementStatus
```typescript
"En revisión" | "Por actualizar" | "Actualizado" | "Cancelado"
```

### EndorsementType
```typescript
"Cambio de domicilio" | "Cancelación" | "Cambio de nombre"
```

### Endorsement Interface
```typescript
interface Endorsement {
  id: string;
  usuario: string;
  tipoEndoso: EndorsementType;
  productoAsegurado: ProductType;
  fechaRegistro: string;
  estatus: EndorsementStatus;
}
```

## Integración con Backend

### Preparación para API
El código está estructurado para facilitar la integración:

1. **Filtros**: La función `handleSearchFilters` puede conectarse directamente a endpoints
2. **Paginación**: Estructura preparada en `EndorsementTableProps`
3. **Datos**: Reemplazar `dummyEndorsements` con llamadas a API
4. **Estados**: Manejo de loading ya implementado

### Puntos de Integración
- `filterEndorsements()` → Reemplazar con llamada a API
- `dummyEndorsements` → Datos del backend
- `pagination` → Datos reales de paginación
- `handleEndorsementClick()` → Validar IDs reales

## Estilos y Colores

### Colores por Estatus
- **En revisión**: `#FF9800` (Naranja)
- **Por actualizar**: `#F44336` (Rojo)
- **Actualizado**: `#4CAF50` (Verde)
- **Cancelado**: `#9E9E9E` (Gris)

### Botones Activos
- Background: `#10265F`
- Color: `#ffffff`
- Font-weight: `bold`

## Testing

Para probar la funcionalidad:

1. **Filtros**: Probar cada botón de estatus
2. **Búsqueda**: Buscar por "Usuario" o "000"
3. **Filtro por Tipo**: Click en icono de filtro entre buscador y ordenamiento
4. **Ordenamiento**: Click en botón de ordenar
5. **Navegación**: Click en cualquier ID
6. **Acciones**: Click en menú de tres puntos

## Próximos Pasos

1. Integrar con API real
2. Implementar página de detalle completa
3. Agregar funcionalidades de edición
4. Implementar paginación real
5. Agregar tests unitarios
