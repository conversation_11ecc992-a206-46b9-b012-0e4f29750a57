import React from "react";
import KpiCards from "./kpiCards/containerKpiCards";
import ClaimsSummary from "./claims-summary/container";
import TableContractedPolicies from "./table-contracted-policies/container";
import ContainerCharts from "./product-performance-and-promotions/containerCharts";

const Dashboard = () => {
  return (
    <section className="bg-white rounded-xl p-6 w-full max-w-7xl h-auto">
      <div>
        <h2 className="text-3xl font-medium mb-6">KPIs Principales</h2>
        <div
          style={{
            fontFamily: "Poppins",
            display: "flex",
            width: "100%",
            justifyContent: "center",
          }}
        >
          <KpiCards />
        </div>
      </div>
      <div className="mt-12">
        <h2 className="text-3xl font-medium mb-5">
          Rendimiento de Productos y Promociones
        </h2>
        <ContainerCharts />
      </div>
      <div className="mt-12">
        <h2 className="text-3xl font-medium mb-6">Resumen de reclamaciones</h2>
        <ClaimsSummary />
      </div>
      <div className="mt-12">
        <h2 className="text-3xl font-medium mb-1">
          Tablas de Pólizas Contratadas
        </h2>
        <TableContractedPolicies />
      </div>
    </section>
  );
};

export default Dashboard;
