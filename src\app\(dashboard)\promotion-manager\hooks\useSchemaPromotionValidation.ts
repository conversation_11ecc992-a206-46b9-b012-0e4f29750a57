import * as yup from "yup";

export const usePromotionSchemaValidation = () => {
  const schema = yup.object().shape({
    name: yup.string().required("El nombre es obligatorio"),
    description: yup.string().required("La descripción es obligatoria"),
    typeDiscount: yup.string().required("El tipo de descuento es obligatorio"),
    discount: yup
      .number()
      .required("El descuento es obligatorio")
      .min(0.01, "El descuento es obligatorio"),
    //.min(0, "El descuento no puede ser negativo"),
    startDate: yup
      .date()
      .nullable() // Permite valores null
      .typeError("La fecha de inicio no es válida")
      .required("La fecha de inicio es obligatoria")
      .test(
        "min-today",
        "La fecha de inicio no puede ser menor a la fecha actual",
        (value) => {
          if (!value) return false;
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const selected = new Date(value);
          selected.setHours(0, 0, 0, 0);
          return selected >= today;
        }
      ),
    endDate: yup
      .date()
      .nullable()
      .typeError("La fecha de finalización no es válida")
      .required("La fecha de finalización es obligatoria")
      .min(
        yup.ref("startDate"),
        "La fecha de finalización debe ser posterior a la fecha de inicio"
      ),
    status: yup.string().required("El estado es obligatorio"),
    code: yup
      .string()
      .required("El código es obligatorio")
      .max(10, "El código debe tener máximo 10 caracteres"),
    //uses: yup.number(),
    maxUses: yup
      .number()
      .typeError("El número máximo de usos debe ser un número")
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : value
      )
      .integer("El número máximo de usos debe ser un número entero")
      .min(1, "Debe haber al menos un uso permitido")
      .required("El número máximo de usos es obligatorio"),
    products: yup
      .array()
      .of(
        yup.object().shape({
          productId: yup.number().required("El ID del producto es obligatorio"),
          insuredAmounts: yup
            .array()
            .of(yup.number().required("El plan es obligatorio"))
            .min(1, "Debe seleccionar al menos un plan")
            .required("Debe seleccionar al menos un plan"),
        })
      )
      .min(1, "Debe asociar al menos un producto")
      .required("Debe seleccionar al menos un producto"),
    //.default([]),
  });

  return { schema };
};
