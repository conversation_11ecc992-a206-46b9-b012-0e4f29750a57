/**
 * Convierte un color hexadecimal a un objeto RGB.
 * @param hex - El color en formato hexadecimal (por ejemplo, "#AFBCC0").
 * @returns Un objeto con las propiedades r, g, b y a.
 */
export const hexToRgb = (hex: string) => {
  // Elimina el símbolo '#' si está presente
  const sanitizedHex = hex.replace("#", "");

  // Divide el color en componentes RGB
  const bigint = parseInt(sanitizedHex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return { r, g, b, a: 1 }; // Incluye el canal alfa con valor predeterminado de 1
};

/**
 * Convierte un color RGB a un objeto HSV.
 * @param r - Componente rojo (0-255).
 * @param g - Componente verde (0-255).
 * @param b - Componente azul (0-255).
 * @returns Un objeto con las propiedades h, s, v y a.
 */
export const rgbToHsv = (r: number, g: number, b: number) => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;

  let h = 0;
  let s = 0;
  const v = max;

  if (delta !== 0) {
    if (max === r) {
      h = ((g - b) / delta) % 6;
    } else if (max === g) {
      h = (b - r) / delta + 2;
    } else {
      h = (r - g) / delta + 4;
    }

    h *= 60;
    if (h < 0) h += 360;

    s = delta / max;
  }

  return { h, s, v, a: 1 }; // Incluye el canal alfa con valor predeterminado de 1
};
