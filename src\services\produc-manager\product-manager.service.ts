import { queryBuilder } from "src/helper/queryBuilder";
import { GetAllProductsTableDto } from "./dtos/get-all-products-table.dto";
import { PRODUCT_MANAGER_ROUTES } from "src/configs/api-routes";
import { AppAlpexApiGateWay } from "../app.equinox.api-getway";
import {
  Product,
  ProductManagerTableResponseDto,
} from "./dtos/get-all-products-table-response.dto";
import { UploadImageAzureResDto } from "./dtos/upload-image-azure.dto";
import {
  AddProductForm,
  EditProductForm,
} from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";
import { GetProductByIdRes } from "./dtos/get-product-by-id";
import { AddProductRes } from "./dtos/add-product-response";
import { DeleteProductResponse } from "./dtos/delete-product-response";
import { Category } from "./dtos/get-categories";
import { ServerError } from "./dtos/server-error.interface";
import { AxiosError } from "axios";
import { CreatePlanDto, CreatePlanResponse } from "./dtos/create-plan";
import { UpdatePlanDto } from "./dtos/update-plan";
import { DuplicateProductResponseDto } from "./dtos/duplicate-product";
import { UploadPolizaResponseDto } from "./dtos/upload-file-poliza-response";

class ProductManagerService {
  public async getProductsPagination(
    params: GetAllProductsTableDto,
    urlQ?: string
  ): Promise<ProductManagerTableResponseDto> {
    try {
      const url = urlQ
        ? urlQ
        : queryBuilder(
            params.filters,
            `${PRODUCT_MANAGER_ROUTES.GET_ALL_PRODUCTS_TABLE}`
          );

      const { data: response } =
        await AppAlpexApiGateWay.get<ProductManagerTableResponseDto>(
          `${url}&currentPage=${params.pagination.currentPage}&pageSize=${params.pagination.pageSize}`
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async uploadImagesAzure(
    params: FormData
  ): Promise<UploadImageAzureResDto> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.post<UploadImageAzureResDto>(
          `${PRODUCT_MANAGER_ROUTES.UPLOAD_IMAGES_AZURE}`,
          params,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async deleteImagesAzure(
    idImage: string
  ): Promise<UploadImageAzureResDto> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.delete<UploadImageAzureResDto>(
          `${PRODUCT_MANAGER_ROUTES.UPLOAD_IMAGES_AZURE}/${idImage}`
        );

      console.log("res delete", response);

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async addProduct(params: AddProductForm): Promise<AddProductRes> {
    try {
      const { data: response } = await AppAlpexApiGateWay.post<AddProductRes>(
        `${PRODUCT_MANAGER_ROUTES.ADD_PRODUCT}`,
        { ...params }
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async getProductById(id: string): Promise<GetProductByIdRes> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.get<GetProductByIdRes>(
          `${PRODUCT_MANAGER_ROUTES.GET_PRODUCT_BY_ID}?id=${id}`
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async updateProduct(
    params: EditProductForm,
    idProduct: string | string[]
  ) {
    try {
      const { data: response } = await AppAlpexApiGateWay.put(
        `${PRODUCT_MANAGER_ROUTES.UPDATE_PRODUCT}?id=${idProduct}`,
        { ...params }
      );

      return response;
    } catch (error: unknown) {
      // Verifica si el error es una instancia de AxiosError
      if (error instanceof AxiosError && error.response) {
        const serverError = error.response.data as ServerError;
        //console.error("Error en updateProduct:", serverError.message);
        throw new Error(serverError.message);
      }

      // Si no hay respuesta del servidor, lanza un error genérico
      throw new Error("Error desconocido al actualizar el producto");
    }
  }

  public async deleteProduct(id: number): Promise<DeleteProductResponse> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.delete<DeleteProductResponse>(
          `${PRODUCT_MANAGER_ROUTES.DELETE_PRODUCT}?id=${id}`
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async getCategories(): Promise<Category[]> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get<Category[]>(
        `${PRODUCT_MANAGER_ROUTES.CATEGORIES}`
      );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async createCategory(type: string): Promise<Category> {
    try {
      const { data: response } = await AppAlpexApiGateWay.post<Category>(
        `${PRODUCT_MANAGER_ROUTES.CATEGORIES}`,
        {
          type,
        }
      );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async deletePlan(id: number): Promise<DeleteProductResponse> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.delete<DeleteProductResponse>(
          `${PRODUCT_MANAGER_ROUTES.PLANS}/${id}`
        );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async duplicateProduct(id: number) {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.post<DuplicateProductResponseDto>(
          `${PRODUCT_MANAGER_ROUTES.DUPLICATE_PRODUCT}`,
          {
            ids: [id],
          }
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async createPlan(payload: CreatePlanDto): Promise<CreatePlanResponse> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.post<CreatePlanResponse>(
          `${PRODUCT_MANAGER_ROUTES.PLANS}`,
          payload
        );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async updatePlan(params: UpdatePlanDto) {
    try {
      const { data: response } = await AppAlpexApiGateWay.put(
        `${PRODUCT_MANAGER_ROUTES.PLANS}`,
        { ...params }
      );

      return response;
    } catch (error: unknown) {
      // Verifica si el error es una instancia de AxiosError
      if (error instanceof AxiosError && error.response) {
        const serverError = error.response.data as ServerError;
        //console.error("Error en updateProduct:", serverError.message);
        throw new Error(serverError.message);
      }

      // Si no hay respuesta del servidor, lanza un error genérico
      throw new Error("Error desconocido al actualizar el plan");
    }
  }

  public async getProductsActive(): Promise<Product[]> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get<Product[]>(
        `${PRODUCT_MANAGER_ROUTES.GET_PRDUCTS_ACTIVE}`
      );
      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async uploadFilePoliza(
    file: File | null,
    productId: string
  ): Promise<string | null> {
    if (!file) return null;

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("productId", productId);

      const { data: response } =
        await AppAlpexApiGateWay.post<UploadPolizaResponseDto>(
          `${PRODUCT_MANAGER_ROUTES.ADD_PRODUCT}/document`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

      return response.url;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async getFilePoliza(productId: number): Promise<string | null> {
    try {
      const { data: response } =
        await AppAlpexApiGateWay.get<UploadPolizaResponseDto>(
          `${PRODUCT_MANAGER_ROUTES.ADD_PRODUCT}/${productId}/document`
        );

      return response.url;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async deleteFilePoliza(productId: number): Promise<void> {
    try {
      await AppAlpexApiGateWay.delete(
        `${PRODUCT_MANAGER_ROUTES.ADD_PRODUCT}/${productId}/document`
      );
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async updateFilePoliza(
    file: File | null,
    productId: string
  ): Promise<string | null> {
    if (!file) return null;

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("productId", productId);

      const { data: response } =
        await AppAlpexApiGateWay.put<UploadPolizaResponseDto>(
          `${PRODUCT_MANAGER_ROUTES.ADD_PRODUCT}/document`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

      return response.url;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }
}

const productManagerService = new ProductManagerService();
export default productManagerService;
