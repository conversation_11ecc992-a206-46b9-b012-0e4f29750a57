import { useEffect, useState } from "react";
import { GetProductByIdRes } from "src/services/produc-manager/dtos/get-product-by-id";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useGetProductById = (id: string) => {
	const [productById, setProductById] = useState<GetProductByIdRes | null>(
		null
	);

	useEffect(() => {
		const getProductById = async () => {
			try {
				if (id) {
					const response = await productManagerService.getProductById(id);
					setProductById(response);
				}
			} catch (error) {
				console.error(error);
			}
		};
		getProductById();
	}, [id]);
	return { productById };
};
