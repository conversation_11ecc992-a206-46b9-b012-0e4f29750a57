import { useEffect, useState } from "react";
import endorsementManagerService from "src/services/endorsement-manager/endorsement-manager.service";

export const useGetEndorsementsOnReview = () => {
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEndorsementsOnReview = async () => {
      try {
        setLoading(true);
        const response = await endorsementManagerService.getAllOnReview();
        console.log("🚀 ~ fetchEndorsementsOnReview ~ response:", response)
        setCount(response.data.count);
      } catch (err) {
        console.error("Error al obtener endosos en revisión:", err);
        setError("No se pudieron cargar los endosos en revisión.");
      } finally {
        setLoading(false);
      }
    };

    fetchEndorsementsOnReview();
  }, []);

  const refetch = async () => {
    try {
      setLoading(true);
      const response = await endorsementManagerService.getAllOnReview();
      console.log("🚀 ~ refetch ~ response:", response)
      setCount(response.data.count);
    } catch (err) {
      console.error("Error al obtener endosos en revisión:", err);
      setError("No se pudieron cargar los endosos en revisión.");
    } finally {
      setLoading(false);
    }
  };

  return { count, loading, error, refetch };
};
