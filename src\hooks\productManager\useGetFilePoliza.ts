import { useState } from "react";
import productManagerService from "src/services/produc-manager/product-manager.service";

/**
 * Hook para obtener la URL del archivo de póliza de un producto.
 */
export function useGetFilePoliza() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);

  const getFilePoliza = async (productId: number) => {
    setLoading(true);
    setError(null);
    try {
      const url = await productManagerService.getFilePoliza(productId);
      setFileUrl(url);
      return url;
    } catch (err) {
      setError((err as Error).message);
      setFileUrl(null);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    getFilePoliza,
    fileUrl,
    setFileUrl,
    loading,
    error,
  };
}
