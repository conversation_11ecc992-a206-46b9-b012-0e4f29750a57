"use client";
import { MenuItem, SelectChangeEvent } from "@mui/material";
import SelectMui from "src/app/components/ui/Select";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useState } from "react";
import { toast } from "sonner";
import ConfirmStatusChangeToast from "src/app/components/ui/toast/ConfirmStatusChangeToast";
import { useUpdateStatusPromotion } from "src/hooks/promotionManager/useUpdateStatusPromotion";

interface SelectPromotionStateProps {
  idPromotion: number;
  status: string;
  onChange?: (status: string) => void;
}

const SelectPromotionState = ({
  idPromotion,
  status,
  onChange,
}: SelectPromotionStateProps) => {
  const statusPromotion = ["Publicada", "Borrador", "Inactiva"];
  const [selectedStatus, setSelectedStatus] = useState(status);
  const { updateStatusPromotion, loading } = useUpdateStatusPromotion();

  const isValidTransition = (from: string, to: string): boolean => {
    if (from === "Inactiva" && to === "Publicada") return false;
    return true;
  };

  const handleConfirmStatusChange = async (
    currentStatus: string,
    newStatus: string,
    t: string | number
  ) => {
    if (!isValidTransition(currentStatus, newStatus)) {
      toast.error(`No se puede cambiar de ${currentStatus} a ${newStatus}.`, {
        style: {
          fontFamily: "Poppins",
          color: "#FF4D49",
        },
      });
      toast.dismiss(t);
      return;
    }
    try {
      const response = await updateStatusPromotion({
        id: idPromotion,
        status: newStatus,
      });
      setSelectedStatus(newStatus);
      onChange?.(newStatus);
      toast.success(response?.message || "Estado actualizado correctamente.", {
        style: {
          fontFamily: "Poppins",
          fontWeight: "bold",
          color: "rgb(103 202 35)",
          background: "rgb(238 251 229)",
          border: "1px solid rgb(238 251 229)",
        },
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const backendMsg = error?.message || "Error al actualizar el estado.";
      toast.error(backendMsg, {
        style: {
          fontFamily: "Poppins",
          fontWeight: "bold",
          color: "rgb(229 69 66)",
          background: "rgb(253 234 233)",
          border: "1px solid rgb(253 234 233)",
        },
      });
    } finally {
      toast.dismiss(t);
    }
  };

  const handleStatusChange = (event: SelectChangeEvent<unknown>) => {
    const newStatus = event.target.value as string;

    toast.custom(
      (t) => (
        <ConfirmStatusChangeToast
          currentStatus={selectedStatus}
          newStatus={newStatus}
          onConfirm={() =>
            handleConfirmStatusChange(selectedStatus, newStatus, t)
          }
          onCancel={() => toast.dismiss(t)}
          message="de la promoción"
        />
      ),
      { duration: Infinity }
    );
  };

  return (
    <div className="">
      <SelectMui
        value={selectedStatus} // Valor actual del estado
        onChange={handleStatusChange}
        fullWidth
        IconComponent={ExpandMoreIcon}
        disabled={loading}
      >
        {statusPromotion.map((item, index) => (
          <MenuItem key={index} value={item} sx={{ fontFamily: "Poppins" }}>
            {item}
          </MenuItem>
        ))}
      </SelectMui>
    </div>
  );
};

export default SelectPromotionState;
