import type {
  AxiosError,
  InternalAxiosRequestConfig,
  AxiosResponse,
} from "axios";
import Cookies from "js-cookie";

export const AppInterceptors = {
  req: (config: InternalAxiosRequestConfig) => {
    const tokenCookie = Cookies.get("token");

    if (tokenCookie) config.headers["Authorization"] = `Bearer ${tokenCookie}`;

    return config;
  },
  reqErr: (err: AxiosError) => {
    return Promise.reject(err);
  },
  res: (res: AxiosResponse) => {
    return res;
  },
  resErr: (err: AxiosError) => {
    if (err.response?.status === 401) {
      console.log("Response error: ", err);
      Cookies.remove("token"); // Elimina el token
      window.localStorage.removeItem("token"); // Limpia el token del almacenamiento local
      window.location.href = "/login"; // Redirige al login
    }

    return Promise.reject(err);
  },
};
