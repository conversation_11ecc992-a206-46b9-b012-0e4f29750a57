"use client";
import type { NextPage } from "next";
import {
	Button,
	Box,
	FormHelperText,
	OutlinedInput,
	InputAdornment,
	IconButton,
} from "@mui/material"; // Importa componentes de Material UI
import Image from "next/image"; // Importa el componente de imagen de Next.js

import HeaderLogoLogin from "../../assets/svg/login/wiki_seguros.svg";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import InputMui from "src/app/components/ui/Input";
import TextMui from "src/app/components/ui/Text";
import Link from "next/link";

import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAuth } from "src/hooks/login/useAuth";
import { useState } from "react";
import Icon from "src/app/components/ui/icon";

const schema = yup.object().shape({
	email: yup.string().email().required("Campo requerido"),
	password: yup.string().required("Campo requerido"),
});
type LoginProps = {
	email: string;
	password: string;
};

const Login: NextPage = () => {
	// ** Context
	const { onLogin } = useAuth();

	// ** local state
	const [loading, setLoading] = useState<boolean>(false);
	const [showPassword, setShowPassword] = useState<boolean>(false);

	const {
		handleSubmit,
		control,
		formState: { errors },
	} = useForm<LoginProps>({ resolver: yupResolver(schema) });

	// Maneja el envío del formulario
	const onSubmit: SubmitHandler<LoginProps> = async (data) => {
		setLoading(true);
		const { email, password } = data;
		await onLogin({ email, password });
		setLoading(false);
	};

	return (
		<Box
			className={`max-w-[586px] z-20 w-[90%] shadow-[0px_2px_4px_rgba(0,_0,_0,_0.2)] rounded-[4px] bg-[#fff] flex flex-row items-start justify-start flex-wrap content-start py-[64px] px-[33px] box-border`}
		>
			<Box className="w-full flex flex-col items-center justify-center gap-y-[18px]">
				<Image
					loading="lazy"
					width={147}
					height={71}
					alt=""
					src={HeaderLogoLogin}
				/>
				<TextMui text="Inicia sesión" type="title" />
				<form
					className="flex flex-col gap-y-[32px] w-full"
					onSubmit={handleSubmit(onSubmit)}
				>
					<Controller
						name="email"
						control={control}
						defaultValue=""
						render={({ field: { value, onChange, onBlur } }) => (
							<InputMui
								fullWidth
								placeholder="Escribe tu correo"
								value={value}
								onChange={onChange}
								onBlur={onBlur}
								error={!!errors.email}
								size="medium"
							/>
						)}
					/>
					<Box>
						<Controller
							name="password"
							control={control}
							defaultValue=""
							render={({ field: { value, onChange, onBlur } }) => (
								<OutlinedInput
									fullWidth
									placeholder="Escribe tu contraseña"
									value={value}
									onChange={onChange}
									onBlur={onBlur}
									error={!!errors.password}
									size="medium"
									type={showPassword ? "text" : "password"}
									endAdornment={
										<InputAdornment position="end">
											<IconButton
												edge="end"
												onMouseDown={(e) => e.preventDefault()}
												onClick={() => setShowPassword(!showPassword)}
											>
												<Icon
													icon={
														showPassword
															? "ant-design:eye-outlined"
															: "ant-design:eye-invisible-outlined"
													}
													fontSize={20}
												/>
											</IconButton>
										</InputAdornment>
									}
									sx={{
										borderRadius: "18px",
										fontSize: "14px",
										fontFamily: "Poppins",
										"&.MuiOutlinedInput-root": {
											"&:hover fieldset": {
												borderColor: "#10265F", // Cambia el color del borde al pasar el mouse
											},
										},
										"&.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
											{
												borderColor: "#10265F",
											},
									}}
								/>
							)}
						/>
						{errors.password || errors.password ? (
							<FormHelperText error sx={{ fontFamily: "Poppins" }}>
								Correo o Contraseña incorrectos
							</FormHelperText>
						) : null}
					</Box>
					<Link href="/reset-password" className="bottom-6 relative">
						<TextMui
							text="¿Olvidaste tu contraseña?"
							type="subtitle"
							className="text-[#AF8CC0] text-sm underline"
						/>
					</Link>
					<Button
						variant="contained"
						type="submit"
						loading={loading}
						loadingPosition="start"
						sx={{
							minWidth: "188px",
							width: "auto",
							alignSelf: "center",
							textTransform: "none",
							color: "#fff",
							fontSize: "16px",
							fontWeight: 100,
							background: "#10265F",
							borderRadius: "200px",
							fontFamily: "Poppins",
						}}
					>
						Ingresar
					</Button>
				</form>
			</Box>
		</Box>
	);
};

export default Login; // Exporta el componente Login como el valor por defecto
