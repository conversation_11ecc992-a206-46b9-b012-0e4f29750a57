"use client";

import { Box, Typography } from "@mui/material";
import ArticleIcon from "@mui/icons-material/Article";
import SavingsIcon from "@mui/icons-material/Savings";
import AutorenewIcon from "@mui/icons-material/Autorenew";
import PaidIcon from "@mui/icons-material/Paid";

const steps = [
  { label: "Documentos", icon: <ArticleIcon /> },
  { label: "Propuesta de\nFiniquito", icon: <SavingsIcon /> },
  { label: "Procesando\nPago", icon: <AutorenewIcon /> },
  { label: "Fin de\nReclamación", icon: <PaidIcon /> },
];

interface Props {
  activeStep?: number;
}

export default function StepperProceso({ activeStep = 0 }: Props) {

  return (
    <div className="w-full overflow-x-auto mx-5 px-8">
      <div className="flex items-center justify-center gap-4 sm:gap-6 min-w-[500px] py-6 my-6">
        {steps.map((step, index) => (
          <div className="flex items-center gap-4 sm:gap-6" key={step.label}>
            {/* Paso */}
            <div className="flex flex-col items-center min-w-[70px]">
              <Box
                sx={{
                  backgroundColor: index <= activeStep ? "#B388EB" : "#F7ECFF",
                  color: index <= activeStep ? "white" : "#9C27B0",
                  width: { xs: 36, sm: 48 },
                  height: { xs: 36, sm: 48 },
                  borderRadius: "9999px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontSize: { xs: 18, sm: 24 },
                }}
              >
                {step.icon}
              </Box>
              <Typography
                variant="body2"
                align="center"
                sx={{
                  fontWeight: index <= activeStep ? 600 : 400,
                  marginTop: 1,
                  whiteSpace: "pre-line",
                  fontFamily: "Poppins",
                  color: "#111827",
                  fontSize: { xs: "0.7rem", sm: "0.9rem" },
                  lineHeight: 1.1,
                }}
              >
                {step.label}
              </Typography>
            </div>

            {/* Línea de conexión (excepto el último paso) */}
            {index < steps.length - 1 && (
              <div className={`h-1.5 w-12 sm:w-28 ${index < activeStep ? 'bg-[#B388EB]' : 'bg-[#F1DEFA]'}`} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
