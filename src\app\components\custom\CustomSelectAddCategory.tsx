import { I<PERSON><PERSON><PERSON>on, MenuItem } from "@mui/material";
import React, { useState, useRef, useEffect } from "react";
import IconifyIcon from "../ui/icon";
import InputMui from "../ui/Input";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { Category } from "src/services/produc-manager/dtos/get-categories";

interface CategorySelectProps {
	value: number;
	onChange: (category: Category) => void;
	onCreateCategory: (type: string) => void;
	error?: string;
	categories: Category[] | null;
}

export function CustomSelectAddCategory({
	categories,
	value,
	onChange,
	onCreateCategory,
}: CategorySelectProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isCreatingNew, setIsCreatingNew] = useState(false);
	const [newCategoryName, setNewCategoryName] = useState("");
	const [localError, setLocalError] = useState<string | undefined>();
	const dropdownRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);

	const selectedCategory = categories?.find((cat) => cat.id === value)

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
				if (isCreatingNew && !newCategoryName) {
					setIsCreatingNew(false);
				}
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [isCreatingNew, newCategoryName]);

	useEffect(() => {
		if (isCreatingNew && inputRef.current) {
			inputRef.current.focus();
		}
	}, [isCreatingNew]);

	const handleCreateCategory = () => {
		// Normaliza el nombre de la categoría
		const normalizedCategoryName = newCategoryName.trim().toLowerCase();
        // Verifica si la categoría ya existe
        const categoryExists = categories?.some(
            (category) => category.type.toLowerCase() === normalizedCategoryName
        );

        if (categoryExists) {
            setLocalError("La categoría ya existe."); // Establece el mensaje de error
			//setIsOpen(true);
            return;
        }

		const categoryLength = newCategoryName.length;
		if (categoryLength > 20) {
			setLocalError("máximo 20 caracteres permitidos");
			return;
		}

		if(localError) {
			return;
		}

        // Si no existe, crea la categoría
		onCreateCategory(newCategoryName.trim());
        setNewCategoryName("");
        setIsCreatingNew(false);
		setIsOpen(false);
        setLocalError(undefined); // Limpia el error
		
    };

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && isCreatingNew) {
			e.preventDefault();
			handleCreateCategory();
		} else if (e.key === "Escape") {
			setIsCreatingNew(false);
			setNewCategoryName("");
			setIsOpen(false);
		}
	};

	return (
		<div className="relative w-full" ref={dropdownRef}>
			<button
				type="button"
				onClick={() => setIsOpen(!isOpen)}
				className={`w-full px-4 py-2 text-left bg-white border rounded-lg flex items-center justify-between h-[44px] ${
					isOpen ? "border-[2px]" : ""
				} hover:border-[#10265F] transition-colors`}
			>
				<span
					className={
						selectedCategory ? "text-gray-900 text-[14px]" : "text-gray-500"
					}
				>
					{selectedCategory ? selectedCategory?.type : "Categoría"}
				</span>

				<KeyboardArrowDownIcon
					className={`w-5 h-5 text-[#727272] absolute right-2 transition-transform ${
						isOpen ? "transform rotate-180" : ""
					}`}
				/>
			</button>

			{isOpen && (
				<div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
					<div className="py-1">
						{categories?.map((category) => (
							<button
								key={category.id}
								onClick={() => {
									onChange({
										id: category.id,
										type: category.type,
									});
									setIsOpen(false);
									setIsCreatingNew(false);
								}}
								className="w-full px-4 py-2 text-left flex items-center justify-between hover:bg-gray-100"
							>
								<span className="font-[Poppins]">{category.type}</span>
							</button>
						))}
						<MenuItem
							onClick={() => setIsCreatingNew(true)}
							sx={{ fontFamily: "Poppins", color: "#10265F" }}
						>
							<IconifyIcon icon={"ic:baseline-plus"} color="#10265F" />
							Nueva Categoría
						</MenuItem>
					</div>

					{isCreatingNew && (
						<div className="p-2 border-t border-gray-200">
							<div className="flex items-center gap-2">
								<InputMui
									size="small"
									customstyle="custom"
									ref={inputRef}
									type="text"
									value={newCategoryName}
									onChange={(e) => {
										if (e.target.value.length <= 20) {
											setLocalError(undefined);
											setNewCategoryName(e.target.value);
										} else {
											setLocalError("máximo 20 caracteres permitidos");
										}
									}}
									onKeyDown={handleKeyDown}
									placeholder="Escribe una nueva categoría..."
									className="flex-1 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2"
								/>
								<IconButton
									onClick={handleCreateCategory}
									disabled={!newCategoryName.trim()}
									size="small"
								>
									<IconifyIcon
										icon={"material-symbols:check-rounded"}
										color={!newCategoryName.trim() ? "#cccc" : "#10265F"}
									/>
								</IconButton>
								<IconButton
									onClick={() => {
										setIsCreatingNew(false);
										setNewCategoryName("");
										setLocalError(undefined);
									}}
									size="small"
								>
									<IconifyIcon
										icon={"material-symbols:close-rounded"}
										color="#10265F"
									/>
								</IconButton>
							</div>
							{localError && (
								<p className="mt-1 text-sm text-red-500 font-[Poppins]">
									{localError}
								</p>
							)}
							{/* <p className="mt-1 text-xs text-gray-500">
								{20 - newCategoryName.length} caracteres restantes
							</p> */}
						</div>
					)}
				</div>
			)}
		</div>
	);
}
