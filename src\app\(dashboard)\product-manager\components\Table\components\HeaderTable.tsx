import { Menu, MenuItem, MenuProps, styled, alpha } from "@mui/material";

import React, { useState } from "react";
import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";
import InputMui from "src/app/components/ui/Input";
import TextMui from "src/app/components/ui/Text";

interface HeaderTableProps {
	onSearchFilters: (key: string, value: string | string[]) => void;
}

interface CustomRadioProps {
	checked: boolean;
	onClick: () => void;
}

const HeaderTable = ({ onSearchFilters }: HeaderTableProps) => {
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
	const [order, setOrder] = useState<boolean>(true);
	const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
	const open = Boolean(anchorEl);
	const handleClick = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};
	const handleClose = () => {
		setAnchorEl(null);
	};

	const handleCategorySelect = (option: string) => {
		setSelectedCategory(option);
		onSearchFilters("category", `[${option}]`);
		handleClose();
	};

	const StyledMenu = styled((props: MenuProps) => (
		<Menu
			elevation={0}
			anchorOrigin={{
				vertical: "bottom",
				horizontal: "right",
			}}
			transformOrigin={{
				vertical: "top",
				horizontal: "right",
			}}
			{...props}
		/>
	))(({ theme }) => ({
		"& .MuiPaper-root": {
			borderRadius: 6,
			marginTop: theme.spacing(1),
			minWidth: 180,
			color: "rgb(55, 65, 81)",
			boxShadow:
				"rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
			"& .MuiMenu-list": {
				padding: "4px 0",
			},
			"& .MuiMenuItem-root": {
				"& .MuiSvgIcon-root": {
					fontSize: 18,
					color: theme.palette.text.secondary,
					marginRight: theme.spacing(1.5),
				},
				"&:active": {
					backgroundColor: alpha(
						theme.palette.primary.main,
						theme.palette.action.selectedOpacity
					),
				},
			},
			...theme.applyStyles("dark", {
				color: theme.palette.grey[300],
			}),
		},
	}));

	const StyledRadio = styled("div")(({}) => ({
		width: 10,
		height: 10,
		borderRadius: "50%",
		border: `1px solid #10265F`,
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		"&:hover": {
			borderColor: "#10265F",
		},
		"&.checked": {
			backgroundColor: "#10265F",
			"&::after": {
				content: '""',
				width: 10,
				height: 10,
				borderRadius: "50%",
				backgroundColor: "#10265F",
			},
		},
	}));

	const categoryOptions = ["Gadget", "Rodada", "Asalto", "Hackeo"];

	const buttonSelectedStyle = {
		"&:focus, &:active": {
			backgroundColor: "#10265F",
			color: "#ffffff",
			fontWeight: "bold",
		},
	};

	const CustomRadio: React.FC<CustomRadioProps> = ({ checked, onClick }) => {
		return (
			<StyledRadio className={checked ? "checked" : ""} onClick={onClick} />
		);
	};
	return (
		<div className="flex flex-col lg:flex-row w-auto h-auto p-[20px] items-center justify-between gap-y-[10px] lg:gap-y-0">
			<div className="flex items-center gap-x-[8px]">
				<ButtonMui
					textbutton="Todos"
					variantstyle="secondary"
					sx={buttonSelectedStyle}
					onClick={() => onSearchFilters("status", "")}
				/>
				<ButtonMui
					textbutton="Activos"
					variantstyle="secondary"
					sx={buttonSelectedStyle}
					onClick={() => onSearchFilters("status", `["${"Publicado"}"]`)}
				/>
				<ButtonMui
					textbutton="Borrador"
					variantstyle="secondary"
					sx={buttonSelectedStyle}
					onClick={() => onSearchFilters("status", `["${"Borrador"}"]`)}
				/>
				<ButtonMui
					textbutton="Archivados"
					variantstyle="secondary"
					sx={buttonSelectedStyle}
					onClick={() => onSearchFilters("status", `["${"Archivado"}"]`)}
				/>
			</div>
			<div className="flex items-center gap-x-[10px]">
				<InputMui
					placeholder="Buscar"
					size="small"
					onChange={(e) => onSearchFilters("productName", e.target.value)}
				/>
				<div>
					<ButtonMui
						textbutton=""
						variantstyle="secondary"
						onClick={handleClick}
						startIcon={<Icon icon={"famicons:filter"} />}
						sx={{
							"& .MuiButton-startIcon": {
								margin: "0px",
							},
							minWidth: "auto",
						}}
						id="basic-button"
						aria-controls={open ? "basic-menu" : undefined}
						aria-haspopup="true"
						aria-expanded={open ? "true" : undefined}
					/>
					<StyledMenu
						id="basic-menu"
						anchorEl={anchorEl}
						open={open}
						onClose={handleClose}
						MenuListProps={{
							"aria-labelledby": "basic-button",
						}}
					>
						{categoryOptions.map((option) => (
							<MenuItem
								key={option}
								onClick={() => handleCategorySelect(`"${option}"`)}
							>
								<CustomRadio
									checked={selectedCategory === `"${option}"`}
									onClick={() => handleCategorySelect(option)}
								/>
								<TextMui
									text={option}
									type="columnTable"
									className="font-normal pl-[10px]"
								/>
							</MenuItem>
						))}
					</StyledMenu>
				</div>
				<ButtonMui
					textbutton=""
					variantstyle="secondary"
					startIcon={<Icon icon={"fluent:arrow-sort-20-filled"} />}
					sx={{
						"& .MuiButton-startIcon": {
							margin: "0px",
						},
						minWidth: "auto",
					}}
					onClick={() => {
						onSearchFilters("order", order ? "DESC" : "ASC");
						setOrder(!order);
					}}
				/>
			</div>
		</div>
	);
};

export default HeaderTable;
