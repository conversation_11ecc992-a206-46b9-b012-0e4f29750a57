"use client";
import React, { useEffect } from "react";
import { useGetProductById } from "src/hooks/productManager/useGetProductById";
import ProductForm from "../../components/Form";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import FormPageLayout from "src/app/components/ui/FormPageLayout/FormPageLayout";

interface IParams {
  params: Promise<{ productId: string }>;
}

export default function EditProductPage({ params }: IParams) {
  const { productId } = React.use(params);

  const { productById } = useGetProductById(productId);
  const setIdProdcut = useAddProductStore((state) => state.setAddProductForm);

  useEffect(() => {
    if (productById) {
      setIdProdcut({ idProduct: productById.id });
    }
  }, [productById, setIdProdcut]);

  return (
    <FormPageLayout title="Edición de Producto" backHref="/product-manager">
      <ProductForm editProduct={productById} />
    </FormPageLayout>
  );
}
