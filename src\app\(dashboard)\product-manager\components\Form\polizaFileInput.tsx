import React, { useEffect, useRef, useState } from "react";
import { Controller, Control, FieldError } from "react-hook-form";
import ButtonMui from "src/app/components/ui/Button";
import IconifyIcon from "src/app/components/ui/icon";
import TextMui from "src/app/components/ui/Text";
import { AddProductForm } from "./interfaces/form.interface";
import { useGetFilePoliza } from "src/hooks/productManager/useGetFilePoliza";
import ConfirmationDialog from "src/app/components/ui/toast/ConfirmationDialog";

interface PolizaFileInputProps {
  control: Control<AddProductForm>;
  error?: FieldError;
  name?: keyof AddProductForm;
  label?: string;
  productId?: number;
}

const PolizaFileInput: React.FC<PolizaFileInputProps> = ({
  control,
  error,
  productId,
  name = "filePoliza",
  label = "Detalle de la póliza",
}) => {
  const { getFilePoliza, fileUrl, setFileUrl, loading } = useGetFilePoliza();
  const [openConfirm, setOpenConfirm] = useState(false);
  const [fileError, setFileError] = useState<string | null>(null);

  const inputFileRef = useRef<HTMLInputElement>(null);

  // Si hay productId, obtenemos el archivo de la póliza al montar
  useEffect(() => {
    if (productId) {
      getFilePoliza(productId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productId]);

  return (
    <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
      <TextMui text={label} type="medium" className="text-[#333333]" />
      <Controller
        name={name}
        control={control}
        render={({ field: { value, onChange } }) => {
          const currentValue = value || fileUrl || null;

          return (
            <div className="flex items-center gap-x-2">
              <input
                ref={inputFileRef}
                id="file-poliza"
                type="file"
                accept=".pdf"
                style={{ display: "none" }}
                onChange={(e) => {
                  const file = e.target.files?.[0] || null;
                  if (file && file.size > 5 * 1024 * 1024) {
                    setFileError("El archivo no puede ser mayor a 5MB");
                    if (inputFileRef.current) inputFileRef.current.value = "";
                    onChange(null);
                    return;
                  }
                  setFileError(null);
                  onChange(file);
                }}
              />
              <label htmlFor="file-poliza" className="flex-1">
                <ButtonMui
                  fullWidth
                  textbutton={
                    currentValue
                      ? "Archivo cargado"
                      : loading
                      ? "Cargando..."
                      : "Subir archivo"
                  }
                  startIcon={
                    currentValue ? undefined : (
                      <IconifyIcon icon="mynaui:upload" />
                    )
                  }
                  endIcon={
                    currentValue ? (
                      <span
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setOpenConfirm(true);
                        }}
                        title="Eliminar archivo"
                        className="flex items-center"
                      >
                        <IconifyIcon
                          icon="mynaui:trash"
                          color="#10265f"
                          width={22}
                        />
                      </span>
                    ) : undefined
                  }
                  variantstyle={currentValue ? "secondary" : "primary"}
                  minheight="46px"
                  component="span"
                  sx={{
                    color: currentValue ? "#000" : "",
                    textDecoration: currentValue ? "underline" : "none",
                  }}
                  onClick={
                    currentValue
                      ? () => {
                          let fileUrlToOpen: string | undefined;
                          if (currentValue instanceof File) {
                            fileUrlToOpen = URL.createObjectURL(currentValue);
                          } else if (typeof currentValue === "string") {
                            fileUrlToOpen = currentValue;
                          }
                          if (fileUrlToOpen)
                            window.open(fileUrlToOpen, "_blank");
                        }
                      : undefined
                  }
                />
              </label>
              <ConfirmationDialog
                show={openConfirm}
                message="¿Seguro que deseas eliminar el documento?"
                onCancel={() => setOpenConfirm(false)}
                onConfirm={async () => {
                  setFileUrl(null);
                  onChange(null);
                  if (inputFileRef.current) {
                    inputFileRef.current.value = "";
                  }
                  setOpenConfirm(false);
                }}
              />
            </div>
          );
        }}
      />
      {/* Mensaje de error personalizado para el archivo */}
      {(fileError || error) && (
        <p className="mt-1 text-sm text-red-500 font-[Poppins]">
          {fileError || (error?.message as string)}
        </p>
      )}
    </div>
  );
};

export default PolizaFileInput;
