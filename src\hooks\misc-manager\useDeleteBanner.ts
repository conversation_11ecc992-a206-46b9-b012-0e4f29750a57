import { useState } from "react";
import miscManagerService from "src/services/misc-manager/misc-manager.service";

export const useDeleteBanner = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteBanner = async (id: number): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await miscManagerService.deleteBanner(id);
      return true;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || "Error al eliminar el banner");
      } else {
        setError("Error al eliminar el banner");
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { deleteBanner, loading, error };
};
