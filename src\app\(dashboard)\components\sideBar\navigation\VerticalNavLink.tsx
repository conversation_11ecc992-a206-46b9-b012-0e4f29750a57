"use client";
// ** React Imports
import { ElementType } from "react";

// ** Next Imports
import Link from "next/link";

// ** MUI Imports
import Box, { BoxProps } from "@mui/material/Box";
import ListItem from "@mui/material/ListItem";
import ListItemButton, {
  ListItemButtonProps,
} from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import { styled } from "@mui/material/styles";
import { Badge } from "@mui/material";

// ** Types
import { NavItems } from "../navigation/types";

import TextMui from "src/app/components/ui/Text";
import Image from "next/image";

// ** Custom Components Imports

export interface Props {
  parent?: boolean;
  item: NavItems;
  collapsed?: boolean;
  setCollapsed?: (collapsed: boolean) => void;
  isTablet?: boolean;
  classItem?: string;
  selectedOption?: string;
  setSelectedOption: React.Dispatch<React.SetStateAction<string>>;
}

// ** Styled Components
export const MenuNavLink = styled(ListItemButton)<
  ListItemButtonProps & {
    component?: ElementType;
    href: string;
    target?: "_blank" | undefined;
    className?: string;
  }
>(({ theme, className }) => ({
  backgroundColor: className === "selected" ? "#ffffff !important" : "",
  width: "100%",
  borderRadius: 8,
  transition: "padding-left .25s ease-in-out",
  "&.active": {
    "&, &:hover": {
      backgroundColor: theme.palette.primary.light,
      "&.Mui-focusVisible": {
        backgroundColor: theme.palette.primary.main,
      },
    },
    "& .MuiTypography-root": {
      fontWeight: 500,
      color: `${theme.palette.common.white} !important`,
    },
    "& .MuiListItemIcon-root": {
      color: `${theme.palette.common.white} !important`,
    },
  },
}));

export const MenuItemTextMetaWrapper = styled(Box)<BoxProps>({
  width: "100%",
  display: "flex",
  alignItems: "center",
  gap: "8px", // Adds consistent spacing between text and badge
  justifyContent: "flex-start", // Aligns items from the start
  transition: "opacity .25s ease-in-out",
  overflow: "visible",
});

export const BadgeStyled = styled(Badge)({
  "& .MuiBadge-badge": {
    backgroundColor: "#FF4D49",
    color: "#FFFFFF",
    fontSize: "10px",
    fontWeight: "bold",
    padding: 0,
    width: "23px",
    height: "23px",
    borderRadius: "50%",
    position: "relative", // Changes from absolute to relative
    transform: "none", // Removes default transform
    right: "auto", // Removes right positioning
    top: "auto", // Removes top positioning
  },
});

const VerticalNavLink = ({ item, setSelectedOption, classItem }: Props) => {
  // ** Hooks

  // const icon = !item.icon ? "mdi:circle" : item.icon;

  const onClickMenu = (e: React.MouseEvent<HTMLElement>) => {
    setSelectedOption(item.title);
    if (item.path === undefined) {
      e.preventDefault();
      e.stopPropagation();
    }
    // isTablet && setCollapsed(!collapsed);
  };
  return (
    <ListItem
      disablePadding
      sx={{
        transition: "padding .25s ease-in-out",
        paddingLeft: "0.75rem",
        paddingRight: "0.75rem",
      }}
    >
      <MenuNavLink
        component={Link}
        href={item.path === undefined ? "/" : `${item.path}`}
        selected={classItem === "selected"}
        onClick={(e) => {
          onClickMenu(e);
        }}
        className={classItem}
      >
        <ListItemIcon
          sx={{
            transition: "margin .25s ease-in-out",
            marginRight: "0.5rem",
            minWidth: "0px",
            color: classItem === "selected" ? "rgba(77, 80, 98, 0.87)" : "",
          }}
        >
          {/* <Icon icon={icon as string} /> */}
          <Image
            src={item.icon}
            width={16}
            height={16}
            loading="lazy"
            alt="Logo"
          />
        </ListItemIcon>

        <MenuItemTextMetaWrapper>
          <TextMui
            text={item.title}
            type="subtitle"
            className={`${classItem === "selected" ? "font-semibold" : ""}`}
          />
          {item.badge && item.badge.show && item.badge.count > 0 && (
            <BadgeStyled
              overlap="circular"
              anchorOrigin={{ vertical: "top", horizontal: "right" }}
              badgeContent={
                item.badge.count > 10 ? "+10" : `+${item.badge.count}`
              }
            >
              <Box sx={{ width: 0, height: 0 }} />
            </BadgeStyled>
          )}
        </MenuItemTextMetaWrapper>
      </MenuNavLink>
    </ListItem>
  );
};

export default VerticalNavLink;
