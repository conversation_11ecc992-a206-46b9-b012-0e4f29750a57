import { ObjectSchema } from "yup";
import * as yup from "yup";
import { AddProductForm } from "../components/Form/interfaces/form.interface";

const inputInfoFormValidations = yup
  .string()
  .required("Campo requerido")
  .max(50, "Máximo 50 caracteres");

const planesProductosSchema = yup.object().shape({
  insuredAmount: yup.string().required("Campo requerido"),
  netPremium: yup.string().required("Campo requerido"),
  deductible: yup.string().required("Campo requerido"),
  iva: yup.string().required("Campo requerido"),
  total: yup.string(),
});

const coveragesIconsSchema = yup.object().shape({
  // nombre: yup.string().required("Campo requerido"),
  // url: yup.string().required("Campo requerido"),
  name: yup.string().optional(), // Ahora es opcional
  url: yup.string().optional(),
});

const coveragesSchema = yup.object().shape({
  name: yup
    .string()
    .required("Campo requerido")
    //.max(30, "Máximo 30 caracteres"),
    .max(60, "Máximo 60 caracteres"),
  icon: coveragesIconsSchema,
  percentageInsuredAmount: yup.string().required("Campo requerido"),
  description: yup
    .string()
    .required("Campo requerido")
    //.max(200, "Máximo 200 caracteres"),
    .max(1000, "Máximo 1000 caracteres"),
});

export const useSchemaValidation = () => {
  const schema: ObjectSchema<AddProductForm> = yup.object().shape({
    name: inputInfoFormValidations,
    idZurich: yup
      .number()
      .typeError("ID del producto debe ser un número")
      .required("ID del producto es requerido")
      .default(0),
    type: yup.string(),
    //.required("Por favor, selecciona o crea una categoría."),
    category_id: yup
      .number()
      .typeError("ID de la categoría debe ser un número")
      //.required("ID de la categoría es requerido")
      .required("Por favor, selecciona una categoría.")
      .notOneOf([0], "Por favor, selecciona una categoría válida.")
      .default(0),
    generalCoverage: yup.string().required("Campo requerido"),
    subtitle: inputInfoFormValidations,
    status: yup.string(),
    additionalDetails: yup.string().optional(),
    mainImage: yup.string().required("Campo requerido"),
    secondaryImage: yup.string().optional().required("Campo requerido"),
    coverageName: yup.string().required("Campo requerido").max(50, "Máximo 50 caracteres"),
    coverageDescription: inputInfoFormValidations,
    color: yup.string().optional(),
    planesProductos: yup
      .array()
      .of(planesProductosSchema)
      .required("Campo requerido"),
    coberturas: yup.array().of(coveragesSchema).required("Campo requerido"),
    filePoliza: yup.mixed<File>().nullable().optional(),
    tipoReclamacionId: yup.number().default(0),
  });

  const editSchema = yup.object().shape({
    ...schema.fields,
    id: yup.number(),
  });

  return { schema, editSchema };
};
