export const acceptedFileTypes = ["image/svg+xml"];

export const convertToBase64 = (file: File) => {
	const data = new Promise((resolve, reject) => {
		let baseURL: string | ArrayBuffer | null = "";
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => {
			baseURL = reader.result;
			resolve(baseURL);
		};
		reader.onerror = (error) => reject(error);
	});

	return data;
};

export const validatePercentageInsuredAmount = (value: string): boolean => {
	const regex = /^-?$|^-?\d{1,3}(\.\d*)?$/;
	if (!value || !regex.test(value)) {
		if (value === "") {
			return true;
		}
		return false;
	}
	const numValue = parseFloat(value);
	if (numValue < 0 || numValue > 100) {
		return false;
	}
	return true;
};

export const allowOnlyNumbers = (e: React.KeyboardEvent<HTMLInputElement>) => {
	const { key } = e;
	if (!/^[0-9]$/.test(key)) {
		e.preventDefault();
	}
};
