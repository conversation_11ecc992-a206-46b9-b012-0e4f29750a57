"use client";
import React, { Fragment, useEffect, useRef, useState } from "react";
import { FilesState, InfoProductFormProps } from "./interfaces/form.interface";
import TextMui from "src/app/components/ui/Text";
import { Controller } from "react-hook-form";
import InputMui from "src/app/components/ui/Input";
import {
	Box,
	FormHelperText,
	Grid2,
	IconButton,
	InputAdornment,
	Tab,
	Tabs,
} from "@mui/material";
import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";
import { NumericFormat } from "react-number-format";
import LoadFileButton from "src/app/components/custom/LoadFileButton";
import {
	acceptedFileTypes,
	validatePercentageInsuredAmount,
} from "../../lib/utils";
import { useUploadImagesAzure } from "src/hooks/productManager/useUploadImagesAzure";
import { UploadImageAzureResDto } from "src/services/produc-manager/dtos/upload-image-azure.dto";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import IconifyIcon from "src/app/components/ui/icon";
import { useAddProductForm } from "../../hooks/useCustomForm";

interface TabPanelProps {
	children?: React.ReactNode;
	index: number;
	value: number;
}

export const TabsStyles = {
	maxWidth: "80%",
	"& .MuiTab-root": {
		color: "#10265F",
		textTransform: "capitalize",
		border: "1px solid #10265F",
		fontFamily: "Poppins",
		fontSize: "14px",
		fontWeight: 400,
		borderRight: "none",
	},
	"& .MuiTab-root:first-of-type": {
		borderTopLeftRadius: "8px",
		borderBottomLeftRadius: "8px",
		borderRight: "none",
	},
	"& .MuiTab-root:last-of-type": {
		borderTopRightRadius: "8px",
		borderBottomRightRadius: "8px",

		borderRight: "1px solid #10265F",
	},
	"& .MuiButtonBase-root.MuiTab-root.Mui-selected": {
		color: "#ffffff",
		backgroundColor: "#10265F",
		textTransform: "capitalize",
	},
};

const CustomTabPanel = ({ children, index, value }: TabPanelProps) => {
	return (
		<div
			role="tabpanel"
			hidden={value !== index}
			id={`simple-tabpanel-${index}`}
			aria-labelledby={`simple-tab-${index}`}
		>
			{value === index && <Box className="wrapper-card-table">{children}</Box>}
		</div>
	);
};

const CoverageForm = ({
	fields,
	hookForm,
	append,
	remove,
	productId,
}: Partial<InfoProductFormProps>) => {
	const { uploadImagesAzure } = useUploadImagesAzure();
	const setResponseCoveageFiles = useAddProductStore(
		(state) => state.setResponseFilesCoverage
	);

	const { handleClickSave } = useAddProductForm();

	const [value, setValue] = useState(0);
	const uploadFilesRef = useRef<HTMLInputElement | null>(null);
	const [uploadedFiles, setUploadedFiles] = useState<Set<string>>(new Set());
	const [filesToUpload, setFilesToUpload] = useState<FilesState>({});
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const [message, setMessage] = useState<boolean>(false);

	const formCoverageIconData = new FormData();
	const handleChange = (event: React.SyntheticEvent, newValue: number) => {
		setValue(newValue);
	};

	const handleFileLoaded =
		(key: string, index: number) => (files: File | File[]) => {
			setFilesToUpload((prevFiles: FilesState) => {
				// Convertimos 'files' en un arreglo si no lo es
				const newFiles = Array.isArray(files) ? files : [files];

				// Obtenemos los archivos existentes para la clave, si no hay, usamos un arreglo vacío
				const existingFiles = prevFiles[`${key}.${index}`] || [];

				// Actualizamos el estado combinando los archivos existentes con los nuevos
				const updatedFiles = [...existingFiles, ...newFiles];

				return { ...prevFiles, [`${key}.${index}`]: updatedFiles };
			});
		};

	const handleDeleteLocalFile = (key: string, index: number) => () => {
		setFilesToUpload((prevFiles: FilesState) => {
			const existingFiles = prevFiles[`${key}.${index}`] || [];
			const updatedFiles = existingFiles.filter(
				(file) => file !== prevFiles[`${key}.${index}`][0]
			);
			formCoverageIconData.delete(`${key}.${index}`);
			return { ...prevFiles, [`${key}.${index}`]: updatedFiles };
		});
	};

	const handleCoverageIcon = async (index: number) => {
		const uploadCoverageIconLoaded =
			filesToUpload[`uploadCoverageIcon.${index}`]?.[0];
		if (uploadCoverageIconLoaded) {
			formCoverageIconData.append("file", uploadCoverageIconLoaded);
			formCoverageIconData.append("name", uploadCoverageIconLoaded.name);
			const response = await uploadImagesAzure(formCoverageIconData);
			if (response) {
				const { url } = response as UploadImageAzureResDto;
				// Actualiza el estado global con el nuevo ícono
				setResponseCoveageFiles((prev) => {
					const updatedFiles = [...prev];
					updatedFiles[index] = {
						name: uploadCoverageIconLoaded.name,
						url, // Asigna la URL generada
					};
					return updatedFiles;
				});

				// Actualiza el estado del formulario con el nuevo ícono
				hookForm?.setValue?.(`coberturas.${index}.icon`, {
					name: uploadCoverageIconLoaded.name,
					url, // Asigna la URL generada
				});
			} else {
				console.error("Error al subir el ícono: No se recibió una URL válida.");
			}
			setUploadedFiles((prev) =>
				new Set(prev).add(`uploadCoverageIcon.${index}`)
			);
		}
	};

	const handleRemoveCoverages = (index: number) => {
		remove?.(index);
		// Ajusta el valor de la pestaña activa después de eliminar un elemento
		if (value >= (fields?.length ?? 0) - 1) {
			setValue((fields?.length ?? 1) - 2);
		}
	};

	useEffect(() => {
		fields?.forEach((_, index) => {
			const key = `uploadCoverageIcon.${index}`;
			const uploadCoverageIconLoaded = filesToUpload[key]?.[0];

			if (uploadCoverageIconLoaded && !uploadedFiles.has(key)) {
				handleCoverageIcon(index);
			}
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [filesToUpload]);

	useEffect(() => {
		fields?.forEach((_, index) => {
			const key = `uploadCoverageIcon.${index}`;
			const uploadCoverageIconLoaded = filesToUpload[key]?.[0];
			if (uploadCoverageIconLoaded) {
				hookForm?.setValue?.(`coberturas.${index}.icon`, {
					name: uploadCoverageIconLoaded.name,
					url: uploadCoverageIconLoaded.name,
				});
			} else {
				// hookForm?.setValue?.(`coberturas.${index}.icon`, {
				// 	name: "",
				// 	url: "",
				// });
			}
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [fields, uploadedFiles, filesToUpload]);
	return (
		<div className="flex flex-col w-full py-[12px] px-[10px] ">
			<div className="flex w-full flex-col gap-y-[4px]">
				<TextMui
					text="Tipos de cobertura"
					type="medium"
					className="text-[#333333]"
				/>
				<div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
					<div className=" w-full flex flex-row justify-between items-center">
						<Tabs
							value={value}
							onChange={handleChange}
							aria-label="basic tabs example"
							variant="scrollable"
							textColor="inherit"
							scrollButtons
							allowScrollButtonsMobile
							TabIndicatorProps={{
								style: {
									display: "none",
									color: "#10265F",
								},
							}}
							sx={TabsStyles}
						>
							{fields?.map((item, index) => (
								<Tab
									key={item.id}
									label={`Cobertura ${index + 1}`}
									icon={
										<Icon
											icon={"mynaui:trash"}
											color="#fff"
											onClick={() => handleRemoveCoverages(index)}
											style={{ display: index === 0 ? "none" : "block" }}
										/>
									}
									iconPosition="end"
								/>
							))}
						</Tabs>

						<div className="flex min-w-[107px] max-w-full min-h-[45px] max-h-[45px]">
							<ButtonMui
								textbutton="Añadir"
								variantstyle="add"
								size="small"
								fullWidth
								startIcon={<Icon icon={"ic:baseline-plus"} />}
								onClick={() => {
									append?.({
										name: "",
										percentageInsuredAmount: "0",
										description: "",
										icon: {
											name: "",
											url: "",
										},
									});
									setValue(fields?.length || 0); // Cambia a la nueva pestaña
								}}
							/>
						</div>
					</div>

					{fields?.map((item, index) => {
						const { [`uploadCoverageIcon.${index}`]: uploadCoverageIcon = [] } =
							filesToUpload;
						const [uploadCoverageIconLoaded] = uploadCoverageIcon;
						const actualIcon =
    						"name" in item && "icon" in item ? item.icon?.url : undefined;
						return (
							<CustomTabPanel key={item.id} value={value} index={index}>
								<div className="flex w-full flex-col gap-y-[12px] border rounded-lg border-[#EBEBEB] p-[12px]">
									<Grid2 container spacing={2}>
										<Grid2 size={{ xs: 12, sm: 12, md: 8 }}>
											<div className="flex w-full flex-col gap-y-[6px]">
												<TextMui
													text="Nombre de la cobertura*"
													type="inputLabel"
												/>
												<Controller
													name={`coberturas.${index}.name`}
													control={hookForm?.control}
													render={({ field: { value, onChange, onBlur } }) => (
														<InputMui
															value={value}
															onChange={onChange}
															onBlur={onBlur}
															placeholder="Ingresa el nombre de la cobertura"
															customstyle="custom"
															error={
																!!hookForm?.errors?.coberturas?.[index]?.name
															}
															helperText={
																hookForm?.errors?.coberturas?.[index]?.name
																	?.message
															}
															slotProps={{
																formHelperText: {
																	sx: { fontFamily: "Poppins", ml: 0 },
																},
															}}
														/>
													)}
												/>
											</div>
										</Grid2>
										<Grid2
											size={{ xs: 12, sm: 12, md: 4 }}
											display={"flex"}
											justifyContent={"flex-end"}
										>
											<div className="flex w-full flex-col gap-y-[12px] justify-end">
												<TextMui
													text="Sublímite"
													type="inputLabel"
													className="text-sm"
												/>
												<Controller
													name={`coberturas.${index}.percentageInsuredAmount`}
													control={hookForm?.control}
													render={({ field: { value, onChange, onBlur } }) => (
														<NumericFormat
															value={value}
															onChange={onChange}
															onBlur={onBlur}
															decimalScale={2}
															allowNegative={false}
															customstyle="custom"
															customInput={InputMui}
															thousandSeparator=","
															decimalSeparator="."
															placeholder="0.00"
															isAllowed={(values) =>
																validatePercentageInsuredAmount(values.value)
															}
															error={
																!!hookForm?.errors?.coberturas?.[index]
																	?.percentageInsuredAmount
															}
															helperText={
																hookForm?.errors?.coberturas?.[index]
																	?.percentageInsuredAmount?.message
															}
															slotProps={{
																formHelperText: {
																	sx: { fontFamily: "Poppins", ml: 0 },
																},
																input: {
																	endAdornment: (
																		<InputAdornment position="start">
																			%
																		</InputAdornment>
																	),
																},
															}}
														/>
													)}
												/>
											</div>
										</Grid2>
									</Grid2>
									<div className="flex w-full flex-col gap-y-[6px]">
										<TextMui text="Descripción*" type="inputLabel" />
										<Controller
											name={`coberturas.${index}.description`}
											control={hookForm?.control}
											render={({ field: { value, onChange, onBlur } }) => (
												<InputMui
													value={value}
													onChange={onChange}
													onBlur={onBlur}
													placeholder="Ingresa la descripción"
													customstyle="custom"
													error={
														!!hookForm?.errors?.coberturas?.[index]?.description
													}
													helperText={
														hookForm?.errors?.coberturas?.[index]?.description
															?.message
													}
													slotProps={{
														formHelperText: {
															sx: { fontFamily: "Poppins", ml: 0 },
														},
													}}
												/>
											)}
										/>
									</div>
									<div className="flex w-full flex-col gap-y-[6px]">
										<TextMui text="Icono*" type="inputLabel" />
										<div className="flex w-full flex-col">
											<div className="flex w-full min-h-[78px] border border-dashed items-center justify-between p-5 ">
												{productId && actualIcon !== undefined ? (
													<div className="flex w-full items-center justify-center">
														<TextMui
															text={"Icono cargado"}
															type="subtitle"
															className="pr-2"
														/>
														<IconButton
															onClick={handleDeleteLocalFile(
																"uploadCoverageIcon",
																index
															)}
														>
															<Icon icon={"mynaui:trash"} color="#10265F" />
														</IconButton>
													</div>
													) : uploadCoverageIconLoaded ? (
													<div className="flex w-full items-center justify-center">
														<TextMui
															text={uploadCoverageIconLoaded.name}
															type="subtitle"
															className="pr-2"
														/>
														<IconButton
															onClick={handleDeleteLocalFile(
																"uploadCoverageIcon",
																index
															)}
														>
															<Icon icon={"mynaui:trash"} color="#10265F" />
														</IconButton>
													</div>
												) : (
													<>
														<div className="flex w-full items-center justify-center">
															<Icon icon={"carbon:image"} color="#AFAFAF" />
															<TextMui
																text="Agregar Icono en formato SVG* (Recomendado 121x135 px)"
																type="subtitle"
																className="pl-2"
															/>
														</div>
														<Controller
															name={`coberturas.${index}.icon`}
															control={hookForm?.control}
															render={() => (
																<LoadFileButton
																	setMessage={setMessage}
																	title="Subir"
																	inputRef={uploadFilesRef}
																	onFileLoaded={handleFileLoaded(
																		"uploadCoverageIcon",
																		index
																	)}
																	acceptedFiles={acceptedFileTypes}
																	maxSizeInMB={20}
																	maxFiles={1}
																	sx={{ minWidth: "105px" }}
																/>
															)}
														/>
													</>
												)}
											</div>
											<FormHelperText
												error={
													!!hookForm?.errors?.coberturas?.[index]?.icon?.name
												}
												sx={{ fontFamily: "Poppins", ml: 0 }}
											>
												{
													hookForm?.errors?.coberturas?.[index]?.icon?.name
														?.message
												}
											</FormHelperText>
										</div>
									</div>
								</div>
							</CustomTabPanel>
						);
					})}
					{productId && (
						<div className="flex w-full items-end justify-end">
							<ButtonMui
								fullWidth
								textbutton="Guardar cambios"
								startIcon={<IconifyIcon icon="ic:baseline-save" />}
								variantstyle="primary"
								minheight="46px"
								onClick={handleClickSave}
								type="submit"
								sx={{ maxWidth: "212px" }}
							/>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default CoverageForm;
