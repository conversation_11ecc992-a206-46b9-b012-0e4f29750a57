import { GridColDef } from "@mui/x-data-grid";
import React from "react";
import TextMui from "src/app/components/ui/Text";

interface ColumnHeaderProps {
  colDef: GridColDef;
}

const ColumnHeader = ({ colDef }: ColumnHeaderProps) => {
  return (
    <div className="flex justify-between items-center relative">
      <TextMui
        text={colDef?.headerName}
        type="columnTable"
        className="whitespace-normal break-words text-center"
      />
    </div>
  );
};

export default ColumnHeader;
