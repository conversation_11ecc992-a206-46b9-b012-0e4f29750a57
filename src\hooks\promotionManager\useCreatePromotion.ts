import { useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";
import { DuplicatePromotionResponse } from "src/services/promotion-manager/dtos/duplicate-promotion-response.dto";
import { CreatePromotionDto } from "src/services/promotion-manager/dtos/create-promotion.dto";

export function useCreatePromotion() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);
  const [data, setData] = useState<DuplicatePromotionResponse | null>(null);

  const createPromotion = async (payload: CreatePromotionDto) => {
    setLoading(true);
    setError(null);
    try {
      const response = await promotionManagerService.createPromotion(payload);
      setData(response);
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || "Error al crear la promoción");
        throw err;
      } else {
        setError("Error al crear la promoción");
        throw new Error("Error al crear la promoción");
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    createPromotion,
    loading,
    error,
    data,
  };
}
