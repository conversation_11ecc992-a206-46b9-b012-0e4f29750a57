# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server with Turbopack (faster builds)
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint for code quality checks

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **UI Library**: Material-UI (MUI) v6 with Emotion styling
- **Styling**: Tailwind CSS + MUI components
- **State Management**: Zustand with persist middleware
- **Forms**: React Hook Form with Yup validation
- **HTTP Client**: Axios with custom instance configuration
- **Authentication**: Cookie-based auth with middleware protection
- **Rich Text**: Quill editor for content creation
- **Icons**: Iconify React
- **Notifications**: Sonner toast library

## Architecture Overview

### Route Structure

- `(auth)` - Authentication routes (login, reset-password)
- `(dashboard)` - Protected dashboard routes with sidebar navigation
- Three main management modules:
  - Dashboard - Analytics and KPI overview
  - Product Manager - Insurance product CRUD operations
  - Promotion Manager - Marketing promotion management
  - Misc Manager - Banner and miscellaneous content

### Key Directories

**State Management**

- `src/store/zustand/` - Zustand stores for form state persistence
- Stores handle complex form data for products and promotions

**Services Layer**

- `src/services/` - API service classes organized by domain
- `src/infrastructure/api/` - Axios configuration and request utilities
- Custom interceptors for auth token handling

**Authentication**

- `src/context/login/AuthContext.tsx` - Auth context provider
- `src/middleware.ts` - Route protection middleware
- Token stored in both localStorage and cookies

**UI Components**

- `src/app/components/ui/` - Reusable UI components (Button, Input, Text, etc.)
- `src/app/components/custom/` - Application-specific components
- Material-UI theme configuration in `src/themes/theme.ts`

### Data Flow Patterns

**Form Management**

- React Hook Form + Yup validation for all forms
- Zustand stores persist form state across navigation
- Custom hooks in each module's `hooks/` directory manage form logic

**API Integration**

- Service classes handle all API communication
- DTOs define request/response interfaces
- Axios interceptors manage authentication headers

**File Upload**

- Azure Blob Storage integration for image uploads
- Next.js Image component configured for Azure domains
- Custom file upload components with progress tracking

## Development Notes

- Uses Turbopack for faster development builds
- TypeScript path mapping configured for `src/*` imports
- ESLint configured with Next.js and TypeScript rules
- All forms use controlled components pattern
- Protected routes redirect to login if unauthenticated
- Toast notifications for user feedback throughout app
