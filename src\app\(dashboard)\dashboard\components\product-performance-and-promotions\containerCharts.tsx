import <PERSON><PERSON>hart from "./barChart";
import Donut<PERSON>hart from "./donutChart";

export default function ContainerCharts() {
  return (
    <div className="w-full flex justify-center items-center">
      <div className="w-full max-w-5xl flex gap-8">
        <div className="flex-1 flex items-center justify-center">
          <div className="w-full bg-white rounded-2xl shadow-[0_7.6px_15.1px_rgba(0,0,0,0.25)] p-6 min-h-[340px] flex items-center justify-center">
            <DonutChart />
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="w-full bg-white rounded-2xl shadow-[0_7.6px_15.1px_rgba(0,0,0,0.25)] p-6 min-h-[340px] flex items-center justify-center">
            <BarChart />
          </div>
        </div>
      </div>
    </div>
  );
}
