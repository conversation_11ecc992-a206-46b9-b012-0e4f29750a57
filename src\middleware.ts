import { NextResponse, NextRequest } from "next/server";

export function middleware(request: NextRequest) {
	const protectedRoutes = [
		"/dashboard",
		"/product-manager",
		"/promotion-manager",
	];

	const isProtectedRoute = protectedRoutes.some((route) =>
		request.nextUrl.pathname.startsWith(route)
	);

	const authToken = request.cookies.get("token")?.value;

	if (!authToken && isProtectedRoute) {
		const response = NextResponse.redirect(new URL("/login", request.url));
		response.cookies.delete("token");
		return response;
	}
	if (authToken && request.nextUrl.pathname.startsWith("/login")) {
		const response = NextResponse.redirect(new URL("/dashboard", request.url));
		return response;
	}
}

export const config = {
	matcher: ["/dashboard", "/product-manager", "/promotion-manager", "/endorsement-manager", "/login"],
};
