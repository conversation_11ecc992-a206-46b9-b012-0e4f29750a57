"use client";
import React from "react";
import Editor from "./Editor";
import Quill from "quill";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";

const Delta = Quill.import("delta");

type QuillEditorProps = {
	onChange: (value: string) => void;
	value: string;
	quillRef: React.RefObject<Quill | null>;
	descriptionProduct?: string;
};

const QuillEditor = ({
	onChange,
	quillRef,
	descriptionProduct,
}: QuillEditorProps) => {
	const setDescription = useAddProductStore((state) => state.setAddProductForm);

	// Use a ref to access the quill instance directly

	const getHTML = () => {
		if (quillRef.current) {
			const html = quillRef.current.root.innerHTML;
			setDescription({ description: html });
			onChange(html);
		}
	};

	return (
		<div className="flex flex-col w-full">
			<Editor
				ref={quillRef}
				defaultValue={new Delta()}
				readOnly={false}
				onTextChange={getHTML}
				descriptionProduct={descriptionProduct}
			/>
		</div>
	);
};

export default QuillEditor;
