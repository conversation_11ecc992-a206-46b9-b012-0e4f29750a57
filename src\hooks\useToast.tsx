// documentation:
// https://sonner.emilkowal.ski/toast

import { toast as sonner, type ExternalToast } from "sonner";
import { CustomSonnerToast } from "src/app/components/ui/toast/CustomSonnerToast";

const DEFAULT_TOAST_OPTIONS: ExternalToast = {
	position: "top-right",
	style: {
		right: 0,
		width: "auto",
	},
};

const getIsWrapText = (text: string) => {
	return text.length > 40;
};

const getIsWrapTitle = (title: string) => {
	return title.length > 30;
};

interface RenderToastProps {
	customToastProps: {
		type: "success" | "error" | "info" | "warning" | "loading";
		message: string;
		iconifyIcon: string;
		title?: string;
	};
	toastObject: {
		t: string | number;
		data?: ExternalToast;
	};
}

export const useToast = () => {
	class Toast {
		private _defaultOptions = (
			customToastProps: RenderToastProps["customToastProps"],
			data?: ExternalToast
		) => ({
			...DEFAULT_TOAST_OPTIONS,
			...(getIsWrapText(customToastProps.message)
				? {
						style: { ...DEFAULT_TOAST_OPTIONS.style, width: "400px" },
						duration: 10000,
				  }
				: {}),
			...data,
		});

		private _renderToast = ({
			customToastProps,
			toastObject,
		}: RenderToastProps) => {
			const { t, data } = toastObject;

			return (
				<CustomSonnerToast
					{...customToastProps}
					isWrapText={getIsWrapText(customToastProps.message)}
					isWrapTitle={getIsWrapTitle(customToastProps.title || "")}
					data={data}
					onClose={() => sonner.dismiss(t)}
				/>
			);
		};

		private _customToast = (
			customToastProps: RenderToastProps["customToastProps"],
			data?: ExternalToast
		) => {
			return sonner.custom(
				(t) =>
					this._renderToast({
						customToastProps,
						toastObject: { t, data },
					}),
				this._defaultOptions(customToastProps, data)
			);
		};

		success = (message: string, title?: string, data?: ExternalToast) => {
			return this._customToast(
				{
					type: "success",
					iconifyIcon: "mdi:check-circle",
					message,
					title,
				},
				data
			);
		};

		error = (message: string, title?: string, data?: ExternalToast) => {
			return this._customToast(
				{
					type: "error",
					iconifyIcon: "mdi:information-outline",
					message,
					title,
				},
				data
			);
		};

		info = (message: string, title?: string, data?: ExternalToast) => {
			return this._customToast(
				{
					type: "info",
					iconifyIcon: "mdi:information-outline",
					message,
					title,
				},
				data
			);
		};

		warning = (message: string, title?: string, data?: ExternalToast) => {
			return this._customToast(
				{
					type: "warning",
					iconifyIcon: "mdi:warning-outline",
					message,
					title,
				},
				data
			);
		};

		loading = (message: string, title?: string, data?: ExternalToast) => {
			return this._customToast(
				{
					type: "loading",
					iconifyIcon: "svg-spinners:ring-resize",
					message,
					title,
				},
				{
					dismissible: false,
					duration: Infinity,
					...data,
				}
			);
		};

		dismissAll = () => {
			sonner.dismiss();
		};

		dismissById = (id: string | number) => {
			sonner.dismiss(id);
		};
	}

	return {
		toast: new Toast(),
		sonner,
	};
};
