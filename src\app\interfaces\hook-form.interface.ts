import type {
	Control,
	FieldErrors,
	FieldValues,
	UseFormClearErrors,
	UseFormGetValues,
	UseFormRegister,
	UseFormReset,
	UseFormResetField,
	UseFormSetError,
	UseFormSetValue,
	UseFormTrigger,
	UseFormWatch,
	useWatch,
} from "react-hook-form";

export interface HookForm<T extends FieldValues> {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	control: Control<T, any>;
	errors: FieldErrors<T>;
	isValid: boolean;
	setValue: UseFormSetValue<T>;
	register: UseFormRegister<T>;
	trigger: UseFormTrigger<T>;
	getValues: UseFormGetValues<T>;
	watch: UseFormWatch<T>;
	useWatch: typeof useWatch;
	setError: UseFormSetError<T>;
	clearErrors: UseFormClearErrors<T>;
	resetField: UseFormResetField<T>;
	reset: UseFormReset<T>;
}
