import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ltip,
  ResponsiveContainer,
  LabelList,
  Cell,
  CartesianGrid,
} from "recharts";

const COLORS = ["#a6a1e0", "#a6dfff", "#c4e1c1", "#d4c3de"];

const data = [
  { name: "Dato 1", value: 170 },
  { name: "Dato 2", value: 154 },
  { name: "Dato 3", value: 210 },
  { name: "Dato 4", value: 125 },
];

export default function SimpleBarChart() {
  return (
    <div className="w-full h-full flex flex-col">
      <div className="text-center text-lg font-normal mb-2 text-[#6D6D6D]">
        Productos más vendidos
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data}>
          <CartesianGrid
            strokeDasharray="3 3"
            horizontal={true}
            vertical={false}
          />
          <XAxis dataKey="name" tick={{ fill: "#6D6D6D" }} />
          <YAxis domain={[0, 250]} ticks={[0, 50, 100, 150, 200, 250]} />
          <Tooltip />
          <Bar dataKey="value" radius={[10, 10, 0, 0]}>
            {data.map((_, idx) => (
              <Cell key={idx} fill={COLORS[idx % COLORS.length]} />
            ))}
            <LabelList dataKey="value" position="top" />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
