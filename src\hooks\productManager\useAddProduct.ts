import { AddProductForm } from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useAddProduct = () => {
	const addProduct = async (params: AddProductForm) => {
		try {
			const response = await productManagerService.addProduct(params);
			return response;
		} catch (error) {
			console.error(error);
		}
	};

	return { addProduct };
};
