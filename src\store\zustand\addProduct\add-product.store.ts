import React from "react";
import { IColor } from "react-color-palette";
import { PlanesProductos } from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";
import { UploadImageInformDto } from "src/services/produc-manager/dtos/upload-image-azure.dto";
import { create, type StateCreator } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface AddProductStoreProps {
  description: string;
  idProduct: number;
  color: IColor;
  mainImageUrl: string;
  selectedCategory: string;
  selectedCategoryId: number;
  status: string;
  setAddProductForm: (form: Partial<AddProductStoreProps>) => void;
  setColor: (color: IColor) => void;
  setStatus: (status: string) => void;
  setResponseFilesCoverage: React.Dispatch<
    React.SetStateAction<UploadImageInformDto[]>
  >;
  responseFilesCoverage: UploadImageInformDto[];
  originalPlanes: PlanesProductos[];
  setOriginalPlanes: (planes: PlanesProductos[]) => void;
}

const addProductStore: StateCreator<AddProductStoreProps> = (set) => ({
  description: "",
  mainImageUrl: "",
  selectedCategory: "",
  selectedCategoryId: 0,
  idProduct: 0,
  status: "Borrador",
  responseFilesCoverage: [],
  setResponseFilesCoverage: (
    responseFilesCoverage: React.SetStateAction<UploadImageInformDto[]>
  ) =>
    set((state) => ({
      responseFilesCoverage:
        typeof responseFilesCoverage === "function"
          ? responseFilesCoverage(state.responseFilesCoverage)
          : responseFilesCoverage,
    })),
  color: {
    hex: "#AFBCC0",
    rgb: { r: 0, g: 0, b: 0, a: 1 },
    hsv: { h: 0, s: 0, v: 0, a: 1 },
  },
  // setColor: (color: IColor) => set({ color }),
  setColor: (color: IColor) =>
    set((state) => ({
      ...state,
      color: { ...color },
    })),
  setStatus: (status: string) => set({ status }),
  setAddProductForm: (form: Partial<AddProductStoreProps>) => set({ ...form }),
  originalPlanes: [],
  setOriginalPlanes: (planes) => set({ originalPlanes: planes }),
});

export const useAddProductStore = create<AddProductStoreProps>()(
  devtools(
    persist(addProductStore, {
      name: "add-product-form",
    })
  )
);
