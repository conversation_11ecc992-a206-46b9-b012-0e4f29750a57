"use client";
import { Grid2, MenuItem } from "@mui/material";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

import InfoProductForm from "./InfoProductForm";
import { useAddProductForm } from "../../hooks/useCustomForm";
import CoverageForm from "./coverageForm";
import PlansForm from "./plansForm";
import SelectMui from "src/app/components/ui/Select";
import TextMui from "src/app/components/ui/Text";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Controller } from "react-hook-form";
import ButtonMui from "src/app/components/ui/Button";
import IconifyIcon from "src/app/components/ui/icon";

import dynamic from "next/dynamic";
import { CustomSelectAddCategory } from "src/app/components/custom/CustomSelectAddCategory";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import { GetProductByIdRes } from "src/services/produc-manager/dtos/get-product-by-id";
import { useGetCategories } from "src/hooks/productManager/useGetCategories";
import productManagerService from "src/services/produc-manager/product-manager.service";
import { Category } from "src/services/produc-manager/dtos/get-categories";
import { hexToRgb, rgbToHsv } from "src/helper/colorUtils";
import ConfirmStatusChangeToast from "src/app/components/ui/toast/ConfirmStatusChangeToast";
import PolizaFileInput from "./polizaFileInput";
import InputMui from "src/app/components/ui/Input";
import { allowOnlyNumbers } from "../../lib/utils";

const ColorPalette = dynamic(() => import("../ColorPicker"), {
  ssr: false,
});

interface IProductFormProps {
  editProduct?: GetProductByIdRes | null;
  onDirtyChange?: (dirty: boolean) => void;
}

const ProductForm = ({ editProduct, onDirtyChange }: IProductFormProps) => {
  const {
    onSubmit,
    hookForm,
    arrayFieldCoverage,
    arrayFieldPlans,
    handleClickSave,
  } = useAddProductForm();

  const [formError, setFormError] = useState<string | null>(null);

  const handleFormSubmit = async () => {
    try {
      if (errorMessage) {
        return;
      }
      setFormError(null); // Limpia el error antes de enviar
      await onSubmit(); // Llama a la función de envío
    } catch (error: unknown) {
      if (error) {
        setFormError(error as string); // Muestra el mensaje de error si es una instancia de Error
      } else {
        setFormError("Ocurrió un error desconocido."); // Manejo genérico para otros tipos de errores
      }
    }
  };

  const setAddProductForm = useAddProductStore(
    (state) => state.setAddProductForm
  );

  const setColor = useAddProductStore((state) => state.setColor);
  const setStatus = useAddProductStore((state) => state.setStatus);

  const setOriginalPlanes = useAddProductStore(
    (state) => state.setOriginalPlanes
  );

  // const categoryProduct = ["Gadget", "Rodada", "Asalto", "Hackeo"];
  const statusProduct = ["Publicado", "Borrador", "Archivado"];
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Reglas de transición de estados
  const isValidTransition = (from: string, to: string): boolean => {
    if (from === "Archivado" && to === "Publicado") return false; // No permitido
    return true; // Permitido en otros casos
  };

  const handleConfirmStatusChange = (
    currentStatus: string,
    newStatus: string,
    onChange: (value: string) => void,
    setErrorMessage: (message: string | null) => void,
    t: string | number
  ) => {
    if (!isValidTransition(currentStatus, newStatus)) {
      setErrorMessage(
        `No se puede cambiar de ${currentStatus} a ${newStatus}.`
      );
      toast.dismiss(t); // Cierra el toast
      return;
    }

    setErrorMessage(null); // Limpia el mensaje de error
    onChange(newStatus); // Actualiza el estado
    toast.dismiss(t); // Cierra el toast
  };

  // Maneja el cambio de estado con confirmación
  const handleProductStatusChange = (
    newStatus: string,
    onChange: (value: string) => void
  ) => {
    const currentStatus = hookForm.watch("status") || "";
    setFormError(null); // Limpia el error antes de mostrar el toast

    // Muestra un toast interactivo para confirmar el cambio
    toast.custom(
      (t) => (
        <ConfirmStatusChangeToast
          currentStatus={currentStatus}
          newStatus={newStatus}
          onConfirm={() =>
            handleConfirmStatusChange(
              currentStatus,
              newStatus,
              onChange,
              setErrorMessage,
              t
            )
          }
          onCancel={() => toast.dismiss(t)} // Cierra el toast
          message="del producto"
        />
      ),
      {
        duration: Infinity,
      }
    );
  };

  const { categories, setCategories } = useGetCategories();
  //console.log("categories", categories);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState(0);

  useEffect(() => {
    if (onDirtyChange) {
      onDirtyChange(true);
    }
  }, [onDirtyChange]);

  useEffect(() => {
    setAddProductForm({
      selectedCategory,
      selectedCategoryId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory, selectedCategoryId]);

  useEffect(() => {
    if (editProduct?.id) {
      // Filtrar los planes para mostrar solo los que no están eliminados
      const filteredPlanesProductos = (
        editProduct.planesProductos ?? []
      ).filter((plan) => plan.deletedAt === null);

      // Guarda los originales en Zustand
      setOriginalPlanes(filteredPlanesProductos);

      const transformedPlanesProductos = filteredPlanesProductos.map(
        (plan) => ({
          ...plan,
          id_plan: plan.id ?? 0, // Renombra 'id' a 'id_plan' y asegura que sea un número
        })
      );

      hookForm.setValue("name", editProduct.name);
      hookForm.setValue("status", editProduct.status);
      hookForm.setValue("subtitle", editProduct.subtitle);
      hookForm.setValue("coverageDescription", editProduct.coverageDescription);
      hookForm.setValue("coverageName", editProduct.coverageName);
      hookForm.setValue("mainImage", editProduct.mainImage);
      hookForm.setValue("secondaryImage", editProduct.secondaryImage);
      hookForm.setValue("coberturas", editProduct.coberturas);
      hookForm.setValue("planesProductos", transformedPlanesProductos);
      hookForm.setValue("idZurich", editProduct.idZurich);
      hookForm.setValue("generalCoverage", editProduct.generalCoverage);
      hookForm.setValue("category_id", editProduct.category_id);
      hookForm.setValue("color", editProduct.color);
      setSelectedCategory(editProduct.type ?? "");
      setSelectedCategoryId(editProduct.category_id ?? 0);

      // Convierte el color hexadecimal al formato IColor
      const color = editProduct.color
        ? {
            hex: editProduct.color,
            rgb: hexToRgb(editProduct.color),
            hsv: rgbToHsv(
              hexToRgb(editProduct.color).r,
              hexToRgb(editProduct.color).g,
              hexToRgb(editProduct.color).b
            ),
          }
        : {
            hex: "#FFFFFF", // Color predeterminado si no hay color
            rgb: { r: 255, g: 255, b: 255, a: 1 },
            hsv: { h: 0, s: 0, v: 1, a: 1 },
          };

      // Actualiza el estado global de color
      setColor(color);
      //Actualiza el estado global del producto
      setStatus(editProduct.status ?? "Borrador");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editProduct]);

  const handleCreateCategory = async (
    type: string,
    onChange: (value: number) => void,
    setSelectedCategory: (value: string) => void,
    setSelectedCategoryId: (value: number) => void,
    setCategories: (categories: Category[]) => void
  ) => {
    try {
      // Llama al servicio para guardar la nueva categoría en la base de datos
      const updatedCategories = await productManagerService.createCategory(
        type
      );

      // Actualiza la lista de categorías con los datos más recientes
      setCategories(
        Array.isArray(updatedCategories)
          ? updatedCategories
          : [updatedCategories]
      );

      // Encuentra la categoría recién creada en la lista actualizada
      const newCategory = Array.isArray(updatedCategories)
        ? updatedCategories.find((cat) => cat.type === type)
        : null;

      if (newCategory) {
        // Selecciona automáticamente la nueva categoría
        onChange(newCategory.id);
        setSelectedCategory(newCategory.type);
        setSelectedCategoryId(newCategory.id);
      }
    } catch (error) {
      console.error("Error al crear la categoría:", error);
      alert("No se pudo crear la categoría. Inténtalo de nuevo.");
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        handleFormSubmit();
      }}
    >
      <Grid2 container spacing={4}>
        <Grid2
          size={{ xs: 12, sm: 8, md: 9.4 }}
          className="flex w-[80%] bg-white rounded-lg flex-col gap-y-[29px]"
        >
          <InfoProductForm
            hookForm={hookForm}
            descriptionProduct={editProduct?.generalCoverage}
            productId={editProduct?.id}
            mainImageEdit={editProduct?.mainImage}
            secondaryImageEdit={editProduct?.secondaryImage}
          />
          <CoverageForm
            fields={arrayFieldCoverage.fields}
            append={arrayFieldCoverage.append}
            remove={arrayFieldCoverage.remove}
            hookForm={hookForm}
            productId={editProduct?.id}
          />
          <PlansForm
            fields={arrayFieldPlans.fields}
            append={arrayFieldPlans.append}
            remove={arrayFieldPlans.remove}
            hookForm={hookForm}
            productId={editProduct?.id}
          />
        </Grid2>
        <Grid2
          size={{ xs: 12, sm: 4, md: 2.6 }}
          className="flex w-[20%] h-full flex-col gap-y-5"
        >
          <ColorPalette />
          <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
            <TextMui
              text="Categoría de producto"
              type="medium"
              className="text-[#333333]"
            />

            <Controller
              name="category_id"
              control={hookForm.control}
              render={({ field: { value, onChange } }) => (
                <CustomSelectAddCategory
                  categories={categories}
                  value={Number(value) || 0}
                  onChange={(category) => {
                    onChange(category.id); // Actualiza el valor en react-hook-form
                    setSelectedCategory(category.type); // Actualiza el estado local si es necesario
                    setSelectedCategoryId(category.id); // Guarda el ID en el estado global
                  }}
                  onCreateCategory={(type) =>
                    handleCreateCategory(
                      type,
                      onChange,
                      setSelectedCategory,
                      setSelectedCategoryId,
                      setCategories
                    )
                  }
                />
              )}
            />

            {/* Mensaje de error debajo del Controller */}
            {hookForm.errors.category_id?.message && (
              <p className="mt-1 text-sm text-red-500 font-[Poppins]">
                {hookForm.errors.category_id.message}
              </p>
            )}

            {/* <CustomSelectAddCategory
							categories={categories}
							value={selectedCategory}
							onChange={setSelectedCategory}
							onCreateCategory={handleCreateCategory}
						/> */}
          </div>
          <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
            <TextMui
              text="Estado del producto"
              type="medium"
              className="text-[#333333]"
            />
            <Controller
              name="status"
              control={hookForm.control}
              render={({ field: { value, onChange } }) => (
                <SelectMui
                  value={value}
                  onChange={(e) =>
                    handleProductStatusChange(
                      e.target.value as string,
                      onChange
                    )
                  }
                  fullWidth
                  IconComponent={ExpandMoreIcon}
                >
                  {statusProduct.map((item, index) => (
                    <MenuItem
                      key={index}
                      value={item}
                      sx={{ fontFamily: "Poppins" }}
                    >
                      {item}
                    </MenuItem>
                  ))}
                </SelectMui>
              )}
            />
            {/* Mensaje de error */}
            {errorMessage && (
              <p className="mt-1 text-sm text-red-500 font-[Poppins]">
                {errorMessage}
              </p>
            )}
            {formError && (
              <div className="mt-2 text-sm text-red-500 font-[Poppins]">
                {formError}
              </div>
            )}
          </div>

          {/* {editProduct?.id && (
            <div className="flex flex-col gap-y-1 bg-white rounded-lg px-[10px] py-3 w-full">
              <TextMui
                text="ID del producto"
                type="medium"
                className="text-[#333333]"
              />
              <input
                type="text"
                value={editProduct.id}
                disabled
                className="mt-1 text-base font-[Poppins] text-gray-900 px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none"
              />
            </div>
          )} */}

          <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
            <TextMui
              text="ID Zurich"
              type="medium"
              className="text-[#333333]"
            />
            <Controller
              name="idZurich"
              control={hookForm.control}
              render={({ field: { value, onChange } }) => (
                <InputMui
                  fullWidth
                  value={value}
                  onChange={onChange}
                  onKeyPress={allowOnlyNumbers}
                  customstyle="custom"
                  placeholder="Máximo 4 dígitos"
                  error={!!hookForm.errors.idZurich}
                  helperText={hookForm.errors.idZurich?.message}
                  slotProps={{
                    formHelperText: {
                      sx: { fontFamily: "Poppins", ml: 0 },
                    },
                    htmlInput: {
                      maxLength: 4,
                    },
                  }}
                />
              )}
            />
          </div>

          <PolizaFileInput
            control={hookForm.control}
            error={hookForm.errors.filePoliza}
            productId={editProduct?.id}
          />

          {editProduct?.id && (
            <div className="flex w-full">
              <ButtonMui
                fullWidth
                textbutton="Guardar cambios"
                startIcon={<IconifyIcon icon="ic:baseline-save" />}
                variantstyle="primary"
                minheight="46px"
                onClick={handleClickSave}
                sx={{ maxWidth: "auto" }}
              />
            </div>
          )}

          <div className="flex w-full flex-col gap-y-[5px]">
            <ButtonMui
              fullWidth
              textbutton="Vista previa"
              startIcon={<IconifyIcon icon="material-symbols:preview" />}
              sx={{ backgroundColor: "#ffffff" }}
              variantstyle="secondary"
              minheight="46px"
            />
            {editProduct?.id ? (
              <ButtonMui
                fullWidth
                textbutton="Eliminar producto"
                startIcon={<IconifyIcon icon="mdi:trash" />}
                variantstyle="primary"
                minheight="46px"
                onClick={() => {
                  console.log("Eliminar producto");
                }}
              />
            ) : (
              <ButtonMui
                fullWidth
                textbutton="Registrar Producto"
                startIcon={<IconifyIcon icon="ic:baseline-save" />}
                variantstyle="primary"
                minheight="46px"
                type="submit"
              />
            )}
          </div>
        </Grid2>
      </Grid2>
    </form>
  );
};

export default ProductForm;
