import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from "sonner";
import { PromotionFormValues } from "../components/Form/interfaces/form.interface";
import { usePromotionSchemaValidation } from "./useSchemaPromotionValidation";
import { GetPromotionByIdRes } from "src/services/promotion-manager/dtos/get-promotion-by-id";
import { toCustomISOString } from "src/helper/customIsoString";
import { useCreatePromotion } from "src/hooks/promotionManager/useCreatePromotion";
import { useUpdatePromotion } from "src/hooks/promotionManager/useUpdatePromotion";

export const usePromotionForm = (
  editPromotion?: GetPromotionByIdRes | null
) => {
  const { schema } = usePromotionSchemaValidation();
  const { createPromotion } = useCreatePromotion();
  const { updatePromotion } = useUpdatePromotion();
  const router = useRouter();

  const methods = useForm<PromotionFormValues>({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: editPromotion
      ? {
          ...editPromotion,
        }
      : {
          name: "",
          description: "",
          typeDiscount: "",
          discount: 0,
          startDate: undefined,
          endDate: undefined,
          status: "Borrador",
          code: "",
          //uses: 0,
          //maxUses: 0,
          products: [],
        },
  });

  function getChangedFields(
    original: Partial<PromotionFormValues>,
    current: PromotionFormValues
  ) {
    const changed: Partial<PromotionFormValues> = {};

    // Siempre incluir products
    changed.products = current.products;

    // Compara campo por campo (excepto products)
    (Object.keys(current) as (keyof PromotionFormValues)[]).forEach((key) => {
      if (key === "products") return; // ya lo agregamos
      // Para fechas, compara como string ISO
      if (key === "startDate" || key === "endDate") {
        const origDate =
          original[key] instanceof Date
            ? (original[key] as Date).toISOString()
            : original[key];
        const currDate =
          current[key] instanceof Date
            ? (current[key] as Date).toISOString()
            : current[key];
        if (origDate !== currDate) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          changed[key] = current[key] as any;
        }
      } else if (original[key] !== current[key]) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        changed[key] = current[key] as any;
      }
    });

    return changed;
  }
  //console.log(methods.formState.errors);
  const handleSave = async (data: PromotionFormValues) => {
    if (editPromotion?.id) {
      // Compara con los valores originales
      const changedFields = getChangedFields(editPromotion, data);

      const payload = {
        id: editPromotion.id,
        ...changedFields,
        startDate: changedFields.startDate
          ? toCustomISOString(changedFields.startDate as Date)
          : undefined,
        endDate: changedFields.endDate
          ? toCustomISOString(changedFields.endDate as Date)
          : undefined,
      };

      Object.keys(payload).forEach(
        (key) =>
          payload[key as keyof typeof payload] === undefined &&
          delete payload[key as keyof typeof payload]
      );

      try {
        await updatePromotion(payload);
        router.push("/promotion-manager");
      } catch (error) {
        console.log("Error al actualizar la promoción:", error);
        let backendMsg = "Error al actualizar la promoción.";
        if (typeof error === "object" && error !== null && "message" in error) {
          backendMsg = String((error as { message: string }).message);
        }
        toast.error(backendMsg, {
          style: {
            fontFamily: "Poppins",
            fontWeight: "bold",
            color: "rgb(229 69 66)",
            background: "rgb(253 234 233)",
            border: "1px solid rgb(253 234 233)",
          },
        });
      }
    } else {
      const fechaInicio = toCustomISOString(new Date(data.startDate), true);
      const fechaFin = toCustomISOString(new Date(data.endDate), true);
      const payload = {
        ...data,
        //startDate: data.startDate ? toCustomISOString(data.startDate) : "",
        //endDate: data.endDate ? toCustomISOString(data.endDate) : "",
        startDate: fechaInicio,
        endDate: fechaFin,
      };
      try {
        await createPromotion(payload);
        router.push("/promotion-manager");
      } catch (error) {
        console.log("Error al crear la promoción:", error);
        toast.error("Error al crear la promoción.", {
          style: {
            fontFamily: "Poppins",
            fontWeight: "bold",
            color: "rgb(229 69 66)",
            background: "rgb(253 234 233)",
            border: "1px solid rgb(253 234 233)",
          },
        });
      }
    }
  };

  return {
    methods, // Devuelve el resultado completo de useForm
    handleSave,
  };
};
