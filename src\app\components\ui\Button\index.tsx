"use client";
import { Button, ButtonProps, styled } from "@mui/material";
import React from "react";
import { poppins } from "src/app/fonts/fonts";

type CustomButtonProps = ButtonProps & {
	variantstyle?: "primary" | "secondary" | "add";
	textbutton: string | undefined;
	minheight?: string;
};

const StyledButton = styled(Button)<CustomButtonProps>(
	({ theme, variantstyle, minheight }) => ({
		borderRadius: "10px",
		textTransform: "none",
		fontFamily: poppins.style.fontFamily,
		fontSize: "14px",
		minHeight: minheight,
		...(variantstyle === "primary" && {
			backgroundColor: "#10265F",

			color: theme.palette.primary.contrastText,
			"&:hover": {
				backgroundColor: "#10265F",
			},
		}),
		...(variantstyle === "secondary" && {
			backgroundColor: "transparent",
			border: "1px solid #10265F",
			color: "#10265F",

			"&:hover": {
				backgroundColor: "transparent",
			},
		}),
		...(variantstyle === "add" && {
			border: "1px solid #10265F",
			backgroundColor: "transparent",
			borderRadius: "200px",
			color: "#10265F",
			"&:hover": {
				backgroundColor: "transparent",
			},
		}),
	})
);
const ButtonMui = ({
	variantstyle,
	textbutton,
	minheight,
	...props
}: CustomButtonProps) => {
	return (
		<StyledButton
			{...props}
			variantstyle={variantstyle}
			textbutton={textbutton}
			minheight={minheight}
		>
			{textbutton}
		</StyledButton>
	);
};

export default ButtonMui;
