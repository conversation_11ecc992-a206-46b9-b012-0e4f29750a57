import React from "react";

interface ClaimCardProps {
  title: string;
  value: number;
  icon?: React.ReactNode;
}

const ClaimCard = ({ title, value, icon }: ClaimCardProps) => (
  <div
    className="
      w-full h-auto
      flex flex-col items-start justify-start
      rounded-[15.8px] bg-white
      shadow-[0_7.6px_15.1px_rgba(0,0,0,0.25)]
      p-4 box-border
    "
  >
    <div className="flex items-center gap-3 w-full">
      {icon && (
        <div className="text-4xl text-[#6d6d6d] flex-shrink-0 flex items-center">
          {icon}
        </div>
      )}
      <span className="text-lg font-medium text-[#6d6d6d]">{title}</span>
    </div>
    <div className="flex flex-col items-left justify-center flex-1 w-full">
      <span className="text-[40px] text-[#333]">
        {value.toLocaleString("es-MX")}
      </span>
    </div>
  </div>
);

export default ClaimCard;
