import axios from "axios";
import { AppInterceptors } from "./interceptors";

export const AppAlpexApiGateWay = axios.create({
	baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api/v1",
	headers: {
		Accept: "application/json",
		"Content-Type": "application/json",
	},
	timeout: 25000,
});

AppAlpexApiGateWay.interceptors.request.use(
	AppInterceptors.req,
	AppInterceptors.reqErr
);

AppAlpexApiGateWay.interceptors.response.use(
	AppInterceptors.res,
	AppInterceptors.resErr
);
