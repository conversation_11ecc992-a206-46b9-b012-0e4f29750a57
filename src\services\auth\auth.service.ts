import { AUTH_ROUTES } from "src/configs/api-routes";
import { AppAlpexApiGateWay } from "../app.equinox.api-getway";
import { LoginDto, LoginResponseDto } from "./dtos/login.dto";

class AuthServices {
	public async login(params: LoginDto): Promise<LoginResponseDto> {
		try {
			const { data: response } =
				await AppAlpexApiGateWay.post<LoginResponseDto>(
					`${AUTH_ROUTES.LOGIN}`,
					{
						...params,
					}
				);
			return response;
		} catch (error) {
			const errMessage = String(error);
			throw new Error(errMessage);
		}
	}
}

export default AuthServices;
