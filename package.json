{"name": "wikicms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack ", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.1", "@iconify/react": "^5.2.0", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@mui/material-nextjs": "^6.3.1", "@mui/x-data-grid": "^7.27.1", "@mui/x-date-pickers": "^8.3.0", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "next": "^15.1.5", "quill": "^2.0.3", "react": "^19.0.0", "react-color": "^2.19.3", "react-color-palette": "^7.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-number-format": "^5.4.3", "recharts": "^2.15.3", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-color": "^3.0.13", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}