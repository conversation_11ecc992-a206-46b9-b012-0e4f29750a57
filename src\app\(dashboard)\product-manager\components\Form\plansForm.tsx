"use client";
import React, { useEffect, useState } from "react";
import {
  InfoProductFormProps,
  PlanesProductos,
} from "./interfaces/form.interface";
import TextMui from "src/app/components/ui/Text";
import { Controller } from "react-hook-form";
import InputMui from "src/app/components/ui/Input";
import { Box, Grid2, InputAdornment, Tab, Tabs } from "@mui/material";
import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";
import { TabsStyles } from "./coverageForm";
import { NumericFormat } from "react-number-format";
import { validatePercentageInsuredAmount } from "../../lib/utils";
import IconifyIcon from "src/app/components/ui/icon";
import { useAddProductForm } from "../../hooks/useCustomForm";
import ConfirmationDialog from "src/app/components/ui/toast/ConfirmationDialog";
import { useDeletePlan } from "src/hooks/productManager/useDeletePlan";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const CustomTabPanel = ({ children, index, value }: TabPanelProps) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box className="wrapper-card-table">{children}</Box>}
    </div>
  );
};

const PlansForm = ({
  fields,
  hookForm,
  append,
  remove,
  productId,
}: Partial<InfoProductFormProps>) => {
  const { handleClickSave } = useAddProductForm();
  const { deletePlan } = useDeletePlan();

  const [value, setValue] = useState(0);

  const [confirmDelete, setConfirmDelete] = useState<{
    index: number;
    show: boolean;
  }>({ index: -1, show: false });

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleRemovePlans = (index: number) => {
    setConfirmDelete({ index, show: true }); // Muestra la alerta de confirmación
  };

  const isPlanesProductos = (
    item: unknown
  ): item is PlanesProductos & Record<"id", string> => {
    return typeof item === "object" && item !== null && "id_plan" in item;
  };

  const handleConfirmDelete = async () => {
    if (confirmDelete.index !== -1) {
      const field = fields?.[confirmDelete.index];
      if (field && isPlanesProductos(field)) {
        const planId = field.id_plan;

        if (!planId) {
          // Si no hay id_plan, elimina solo localmente
          remove?.(confirmDelete.index);
          setValue((prevValue) => {
            const newLength = (fields?.length ?? 0) - 1;
            return prevValue >= newLength
              ? Math.max(newLength - 1, 0)
              : prevValue;
          });
          setConfirmDelete({ index: -1, show: false });
          return;
        }

        try {
          await deletePlan(Number(planId));
          remove?.(confirmDelete.index);
          setValue((prevValue) => {
            const newLength = (fields?.length ?? 0) - 1;
            return prevValue >= newLength
              ? Math.max(newLength - 1, 0)
              : prevValue;
          });
          setConfirmDelete({ index: -1, show: false });
        } catch (error) {
          console.error("Error al eliminar el plan:", error);
        }
      } else {
        // Si no es un plan válido, elimina localmente y cierra el modal
        remove?.(confirmDelete.index);
        setValue((prevValue) => {
          const newLength = (fields?.length ?? 0) - 1;
          return prevValue >= newLength
            ? Math.max(newLength - 1, 0)
            : prevValue;
        });
        setConfirmDelete({ index: -1, show: false });
      }
    }
  };

  useEffect(() => {
    fields?.forEach((item, index) => {
      const tieneIva = hookForm?.watch?.(`planesProductos.${index}.tieneIva`);
      const iva = hookForm?.watch?.(`planesProductos.${index}.iva`);
      if (!tieneIva && iva !== "0") {
        hookForm?.setValue?.(`planesProductos.${index}.iva`, "0");
      }
    });
  }, [fields, hookForm]);

  return (
    <div className="flex flex-col w-full py-[12px] px-[10px]">
      <div className="flex w-full flex-col gap-y-[4px]">
        <TextMui text="Planes" type="medium" className="text-[#333333]" />
        <div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
          <div className=" w-full flex flex-row justify-between">
            <Tabs
              value={value}
              onChange={handleChange}
              aria-label="basic tabs example"
              variant="scrollable"
              scrollButtons
              allowScrollButtonsMobile
              TabIndicatorProps={{
                style: {
                  display: "none",
                  color: "#10265F",
                },
              }}
              sx={TabsStyles}
            >
              {fields?.map((item, index) => (
                <Tab
                  key={item.id}
                  label={`Plan ${index + 1}`}
                  icon={
                    value === index && fields.length > 1 ? (
                      <Icon
                        icon={"mynaui:trash"}
                        color="#fff"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemovePlans(index);
                        }}
                        style={{
                          display: "block",
                          cursor: "pointer",
                        }}
                      />
                    ) : undefined
                  }
                  iconPosition="end"
                />
              ))}
            </Tabs>
            <div className="flex min-w-[107px] max-w-full min-h-[45px] max-h-[45px]">
              <ButtonMui
                textbutton="Añadir"
                variantstyle="add"
                size="small"
                fullWidth
                startIcon={<Icon icon={"ic:baseline-plus"} />}
                onClick={() => {
                  append?.({
                    netPremium: "0",
                    insuredAmount: "0",
                    deductible: "0",
                    iva: "0",
                    tieneIva: false,
                    total: "0",
                  });
                  setValue(fields?.length || 0); // Cambia a la nueva pestaña
                }}
              />
            </div>
          </div>

          {fields?.map((item, index) => (
            <CustomTabPanel key={item.id} value={value} index={index}>
              <div className="flex w-full flex-col gap-y-[12px] border rounded-lg border-[#EBEBEB] p-[12px]">
                <Grid2 container spacing={2}>
                  <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
                    <div className="flex w-full flex-col gap-y-[6px]">
                      <TextMui
                        text="Prima neta"
                        type="inputLabel"
                        className="text-sm"
                      />
                      <Controller
                        name={`planesProductos.${index}.netPremium`}
                        control={hookForm?.control}
                        render={({ field: { value, onChange, onBlur } }) => (
                          <NumericFormat
                            value={value}
                            onChange={onChange}
                            onBlur={onBlur}
                            decimalScale={2}
                            customstyle="custom"
                            customInput={InputMui}
                            thousandSeparator=","
                            decimalSeparator="."
                            placeholder="0.00"
                            allowNegative={false}
                            error={
                              !!hookForm?.errors?.planesProductos?.[index]
                                ?.netPremium
                            }
                            helperText={
                              hookForm?.errors?.planesProductos?.[index]
                                ?.netPremium?.message
                            }
                            slotProps={{
                              formHelperText: {
                                sx: { fontFamily: "Poppins", ml: 0 },
                              },
                              input: {
                                endAdornment: (
                                  <InputAdornment position="start">
                                    $
                                  </InputAdornment>
                                ),
                              },
                            }}
                          />
                        )}
                      />
                    </div>
                  </Grid2>
                  <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
                    <div className="flex w-full flex-col gap-y-[6px]">
                      <TextMui
                        text="Suma asegurada"
                        type="inputLabel"
                        className="text-sm"
                      />
                      <Controller
                        name={`planesProductos.${index}.insuredAmount`}
                        control={hookForm?.control}
                        render={({ field: { value, onChange, onBlur } }) => (
                          <NumericFormat
                            value={value}
                            onChange={onChange}
                            onBlur={onBlur}
                            decimalScale={2}
                            customstyle="custom"
                            customInput={InputMui}
                            thousandSeparator=","
                            decimalSeparator="."
                            placeholder="0.00"
                            allowNegative={false}
                            error={
                              !!hookForm?.errors?.planesProductos?.[index]
                                ?.insuredAmount
                            }
                            helperText={
                              hookForm?.errors?.planesProductos?.[index]
                                ?.insuredAmount?.message
                            }
                            slotProps={{
                              formHelperText: {
                                sx: { fontFamily: "Poppins", ml: 0 },
                              },
                              input: {
                                endAdornment: (
                                  <InputAdornment position="start">
                                    $
                                  </InputAdornment>
                                ),
                              },
                            }}
                          />
                        )}
                      />
                    </div>
                  </Grid2>
                  <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
                    <div className="flex w-full flex-col gap-y-[6px]">
                      <TextMui
                        text="Deducible sobre la pérdida"
                        type="inputLabel"
                        className="text-sm"
                      />
                      <Controller
                        name={`planesProductos.${index}.deductible`}
                        control={hookForm?.control}
                        render={({ field: { value, onChange, onBlur } }) => (
                          <NumericFormat
                            value={value}
                            onChange={onChange}
                            onBlur={onBlur}
                            decimalScale={2}
                            customstyle="custom"
                            customInput={InputMui}
                            thousandSeparator=","
                            decimalSeparator="."
                            placeholder="0.00"
                            allowNegative={false}
                            isAllowed={(values) =>
                              validatePercentageInsuredAmount(values.value)
                            }
                            error={
                              !!hookForm?.errors?.planesProductos?.[index]
                                ?.deductible
                            }
                            helperText={
                              hookForm?.errors?.planesProductos?.[index]
                                ?.deductible?.message
                            }
                            slotProps={{
                              formHelperText: {
                                sx: { fontFamily: "Poppins", ml: 0 },
                              },
                              input: {
                                endAdornment: (
                                  <InputAdornment position="start">
                                    %
                                  </InputAdornment>
                                ),
                              },
                            }}
                          />
                        )}
                      />
                    </div>
                  </Grid2>
                  <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
                    <div className="flex w-full flex-col gap-y-[6px]">
                      <TextMui
                        text="IVA"
                        type="inputLabel"
                        className="text-sm"
                      />
                      <Controller
                        name={`planesProductos.${index}.iva`}
                        control={hookForm?.control}
                        render={({ field: { value, onChange, onBlur } }) => {
                          const tieneIva = hookForm?.watch?.(
                            `planesProductos.${index}.tieneIva`
                          );
                          return (
                            <NumericFormat
                              value={tieneIva ? value : "0"}
                              onChange={onChange}
                              onBlur={onBlur}
                              decimalScale={2}
                              customstyle="custom"
                              customInput={InputMui}
                              thousandSeparator=","
                              decimalSeparator="."
                              placeholder="0.00"
                              allowNegative={false}
                              disabled={!tieneIva}
                              isAllowed={(values) =>
                                validatePercentageInsuredAmount(values.value)
                              }
                              error={
                                !!hookForm?.errors?.planesProductos?.[index]
                                  ?.iva
                              }
                              helperText={
                                hookForm?.errors?.planesProductos?.[index]?.iva
                                  ?.message
                              }
                              slotProps={{
                                formHelperText: {
                                  sx: { fontFamily: "Poppins", ml: 0 },
                                },
                                input: {
                                  endAdornment: (
                                    <InputAdornment position="start">
                                      %
                                    </InputAdornment>
                                  ),
                                },
                              }}
                            />
                          );
                        }}
                      />
                    </div>
                  </Grid2>

                  <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
                    <div className="flex items-center gap-x-2 mt-2 ml-3 whitespace-nowrap">
                      <Controller
                        name={`planesProductos.${index}.tieneIva`}
                        control={hookForm?.control}
                        defaultValue={false}
                        render={({ field: { value, onChange } }) => (
                          <input
                            type="checkbox"
                            checked={!!value}
                            onChange={(e) => {
                              onChange(e.target.checked);
                              if (e.target.checked) {
                                hookForm?.setValue?.(
                                  `planesProductos.${index}.iva`,
                                  "16"
                                );
                              } else {
                                hookForm?.setValue?.(
                                  `planesProductos.${index}.iva`,
                                  ""
                                );
                              }
                            }}
                            className="scale-[1.4] accent-[#10265F] rounded border border-[#D9D9D9] checked:bg-[#10265F] checked:border-[#10265F] focus:ring-0"
                          />
                        )}
                      />
                      <span className="text-sm font-[Poppins] ml-1 text-[#AFAFAF]">
                        Cobrar impuestos sobre la venta de este producto
                      </span>
                    </div>
                  </Grid2>
                </Grid2>
              </div>
            </CustomTabPanel>
          ))}

          {productId && (
            <div className="flex w-full items-end justify-end">
              <ButtonMui
                fullWidth
                textbutton="Guardar cambios"
                startIcon={<IconifyIcon icon="ic:baseline-save" />}
                variantstyle="primary"
                minheight="46px"
                onClick={handleClickSave}
                type="submit"
                sx={{ maxWidth: "212px" }}
              />
            </div>
          )}

          <ConfirmationDialog
            show={confirmDelete.show}
            message="¿Estás seguro de que deseas eliminar este plan?"
            onConfirm={handleConfirmDelete} // Llama a la función para confirmar la eliminación
            onCancel={() => setConfirmDelete({ index: -1, show: false })} // Oculta la alerta
          />
        </div>
      </div>
    </div>
  );
};

export default PlansForm;
