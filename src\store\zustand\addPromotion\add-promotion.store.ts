import { create, type StateCreator } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface AddPromotionStoreProps {
  id: number;
  name: string;
  description: string;
  status: string;
  startDate: Date | null;
  endDate: Date | null;
  typeDiscount: string;
  discount: number;
  code: string;
  uses: number;
  maxUses: number;
  products: string[]; // O el tipo que uses para productos asociados

  setAddPromotionForm: (form: Partial<AddPromotionStoreProps>) => void;
  setStatus: (status: string) => void;
}

const addPromotionStore: StateCreator<AddPromotionStoreProps> = (set) => ({
  id: 0,
  name: "",
  description: "",
  status: "Borrador",
  startDate: null,
  endDate: null,
  typeDiscount: "",
  discount: 0,
  code: "",
  uses: 0,
  maxUses: 1,
  products: [],
  setAddPromotionForm: (form: Partial<AddPromotionStoreProps>) =>
    set({ ...form }),
  setStatus: (status: string) => set({ status }),
});

export const useAddPromotionStore = create<AddPromotionStoreProps>()(
  devtools(
    persist(addPromotionStore, {
      name: "add-promotion-form",
    })
  )
);
