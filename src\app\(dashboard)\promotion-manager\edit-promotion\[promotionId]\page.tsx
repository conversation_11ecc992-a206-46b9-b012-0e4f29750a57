"use client";
import React, { useEffect } from "react";
import { useGetPromotionById } from "src/hooks/promotionManager/useGetPromotionById";
import PromotionForm from "../../components/Form";
import { useAddPromotionStore } from "src/store/zustand/addPromotion/add-promotion.store";
import FormPageLayout from "src/app/components/ui/FormPageLayout/FormPageLayout";

interface IParams {
  params: Promise<{ promotionId: string }>;
}

export default function EditPromotionPage({ params }: IParams) {
  const { promotionId } = React.use(params);
  const { promotionById } = useGetPromotionById(promotionId);
  const setIdPromotion = useAddPromotionStore(
    (state) => state.setAddPromotionForm
  );

  useEffect(() => {
    if (promotionById) {
      setIdPromotion({ id: promotionById.id });
    }
  }, [promotionById, setIdPromotion]);

  return (
    <FormPageLayout title="Edición de Promoción" backHref="/promotion-manager">
      <PromotionForm editPromotion={promotionById} />
    </FormPageLayout>
  );
}
