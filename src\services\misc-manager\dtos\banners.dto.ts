export interface IBanner {
  id: number;
  url_banner: string;
  linkUrl: string;
  order: number;
  isActive: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  userId: number;
}

export interface GetBannersResponse {
  message: string;
  data: IBanner[];
}

export interface CreateBannerPayload {
  url_banner: string;
  linkUrl: string;
  order: number;
}

export interface BannerFormItem {
  id?: number;
  url_banner: string;
  linkUrl: string;
  order?: number | null;
}

export interface PayloadBannerSubmit {
  banners?: BannerFormItem[];
}
