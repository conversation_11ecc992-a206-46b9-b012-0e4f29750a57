import { CreatePlanDto, CreatePlanResponse } from "./dtos/create-plan";
import { PlanesProductos } from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";
import { UpdatePlanDto } from "./dtos/update-plan";

// Función utilitaria para calcular el total con IVA
function calcularTotalConIva(
  netPremium: string | number,
  iva: string | number
): string {
  const net = parseFloat(String(netPremium));
  const ivaNum = parseFloat(String(iva));
  return (net + (ivaNum / 100) * net).toString();
}

function normalizeDeductible(value: string | number): string {
  const str = String(value).replace(/,/g, "").replace(/^0+/, "");
  return str === "" ? "0" : str;
}

export const createPlans = async (
  planesProductos: PlanesProductos[],
  productId: number,
  addPlan: (payload: CreatePlanDto) => Promise<CreatePlanResponse | undefined> // Ajusta el tipo aquí
) => {
  try {
    for (const plan of planesProductos) {
      const total = calcularTotalConIva(plan.netPremium, plan.iva);

      const payload: CreatePlanDto = {
        productId,
        insuredAmount: plan.insuredAmount,
        netPremium: plan.netPremium,
        deductible: normalizeDeductible(plan.deductible),
        iva: plan.iva,
        tieneIva: true,
        total,
      };

      // Llama al endpoint para crear el plan
      const response = await addPlan(payload);
      if (response) {
        console.log("Plan creado:", response.data);
      }
    }
    console.log("Planes creados exitosamente");
  } catch (error) {
    console.error("Error al crear los planes:", error);
    throw error;
  }
};

export const updatePlans = async (
  planesProductos: PlanesProductos[],
  originalPlanes: PlanesProductos[],
  productId: number,
  addPlan: (payload: CreatePlanDto) => Promise<CreatePlanResponse | undefined>,
  updatePlan: (id: number, payload: UpdatePlanDto) => Promise<void>
) => {
  function normalizeNumberString(value: string | number): string {
    // Convierte a string, quita comas y ceros a la izquierda
    return String(value)
      .replace(/,/g, "")
      .replace(/^0+(\d)/, "$1");
  }

  function isPlanChanged(original: PlanesProductos, edited: PlanesProductos) {
    return (
      normalizeNumberString(original.insuredAmount) !==
        normalizeNumberString(edited.insuredAmount) ||
      normalizeNumberString(original.netPremium) !==
        normalizeNumberString(edited.netPremium) ||
      normalizeNumberString(original.iva) !==
        normalizeNumberString(edited.iva) ||
      normalizeNumberString(original.total ?? "0") !==
        normalizeNumberString(edited.total ?? "0")
    );
  }
  try {
    for (const plan of planesProductos) {
      if (plan.id) {
        const original = originalPlanes.find((p) => p.id === plan.id);
        if (original && !isPlanChanged(original, plan)) {
          // Si no hay cambios, no actualices
          continue;
        }
        // Si el plan tiene un ID, actualízalo
        let payload: UpdatePlanDto;
        if (plan.tieneIva) {
          payload = {
            plans: [
              {
                id: plan.id,
                insuredAmount:
                  typeof plan.insuredAmount === "string"
                    ? plan.insuredAmount.replace(/,/g, "")
                    : plan.insuredAmount,
                netPremium:
                  typeof plan.netPremium === "string"
                    ? plan.netPremium.replace(/,/g, "")
                    : plan.netPremium,
                deductible: normalizeDeductible(plan.deductible),
                tieneIva: plan.tieneIva ?? false,
                iva: plan.iva,
                total: calcularTotalConIva(
                  typeof plan.netPremium === "string"
                    ? plan.netPremium.replace(/,/g, "")
                    : plan.netPremium,
                  plan.iva
                ),
              },
            ],
          };

          console.log("Actualizando plan con iva:", payload);
          await updatePlan(plan.id, payload);
        } else {
          payload = {
            plans: [
              {
                id: plan.id,
                insuredAmount:
                  typeof plan.insuredAmount === "string"
                    ? plan.insuredAmount.replace(/,/g, "")
                    : plan.insuredAmount,
                netPremium:
                  typeof plan.netPremium === "string"
                    ? plan.netPremium.replace(/,/g, "")
                    : plan.netPremium,
                deductible: normalizeDeductible(plan.deductible),
                tieneIva: plan.tieneIva ?? false,
                total: calcularTotalConIva(
                  typeof plan.netPremium === "string"
                    ? plan.netPremium.replace(/,/g, "")
                    : plan.netPremium,
                  "0"
                ),
              },
            ],
          };

          await updatePlan(plan.id, payload);
        }
      } else {
        // Si el plan no tiene un ID, créalo
        const payload: CreatePlanDto = {
          productId,
          insuredAmount: plan.insuredAmount,
          netPremium: plan.netPremium,
          deductible: normalizeDeductible(plan.deductible),
          iva: plan.iva,
          tieneIva: true,
          total: calcularTotalConIva(
            typeof plan.netPremium === "string"
              ? plan.netPremium.replace(/,/g, "")
              : plan.netPremium,
            plan.iva
          ),
        };

        await addPlan(payload);
      }
    }
    console.log("Planes actualizados exitosamente");
  } catch (error) {
    console.error("Error al actualizar los planes:", error);
    throw error;
  }
};
