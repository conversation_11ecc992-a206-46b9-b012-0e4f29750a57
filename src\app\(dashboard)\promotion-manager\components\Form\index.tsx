"use client";
import { Grid2 } from "@mui/material";
import PromotionInfo from "./promotionInfo";
import PromotionPeriod from "./promotionPeriod";
import PromotionDiscounts from "./promotionDiscounts";
import PromotionState from "./promotionState";
import PromotionActions from "./promotionActions";
import styles from "./index.module.css";
import { usePromotionForm } from "../../hooks/usePromotionForm";
import { FormProvider } from "react-hook-form";
import { GetPromotionByIdRes } from "src/services/promotion-manager/dtos/get-promotion-by-id";
import { useEffect } from "react";
import { ProductAssociatedPromotion } from "./interfaces/form.interface";
import { PromotionProductFromApi } from "src/services/promotion-manager/dtos/get-promotion-by-id";

interface PromotionFormProps {
  editPromotion?: GetPromotionByIdRes | null;
  onDirtyChange?: (dirty: boolean) => void;
}

const PromotionForm = ({
  editPromotion,
  onDirtyChange,
}: PromotionFormProps) => {
  const { methods, handleSave } = usePromotionForm(editPromotion);

  useEffect(() => {
    if (onDirtyChange) {
      onDirtyChange(true);
    }
  }, [onDirtyChange]);

  useEffect(() => {
    if (editPromotion?.id) {
      // Transforma fechas a objetos Date
      const startDate = editPromotion.startDate
        ? new Date(editPromotion.startDate)
        : null;
      const endDate = editPromotion.endDate
        ? new Date(editPromotion.endDate)
        : null;

      const products = Array.isArray(editPromotion.products)
        ? editPromotion.products.map(
            (
              p: ProductAssociatedPromotion | PromotionProductFromApi
            ): ProductAssociatedPromotion => {
              if (
                typeof (p as ProductAssociatedPromotion).productId ===
                  "number" &&
                Array.isArray((p as ProductAssociatedPromotion).insuredAmounts)
              ) {
                return {
                  productId: (p as ProductAssociatedPromotion).productId,
                  insuredAmounts: (p as ProductAssociatedPromotion)
                    .insuredAmounts,
                };
              }

              return {
                productId: (p as PromotionProductFromApi).id,
                insuredAmounts: Array.isArray(
                  (p as PromotionProductFromApi).planes
                )
                  ? (p as PromotionProductFromApi).planes.map(
                      (plan) => plan.plan_id
                    )
                  : [],
              };
            }
          )
        : [];
      methods.setValue("products", products);
      methods.setValue("name", editPromotion.name);
      methods.setValue("description", editPromotion.description);
      methods.setValue("typeDiscount", editPromotion.typeDiscount);
      methods.setValue("discount", editPromotion.discount);
      if (startDate) {
        methods.setValue("startDate", startDate);
      }
      if (endDate) {
        methods.setValue("endDate", endDate);
      }
      methods.setValue("status", editPromotion.status);
      methods.setValue("code", editPromotion.code);
      methods.setValue("uses", editPromotion.uses);
      methods.setValue("maxUses", editPromotion.maxUses);
    }
  }, [editPromotion, methods]);
  return (
    <FormProvider {...methods}>
      <form>
        <Grid2 container spacing={4}>
          <Grid2
            size={{ xs: 12, sm: 8, md: 9.4 }}
            className="flex w-[80%] bg-white rounded-lg flex-col gap-y-[29px]"
          >
            <section className={styles.bloque}>
              <PromotionInfo />
              <PromotionPeriod />
              <PromotionDiscounts />
            </section>
          </Grid2>
          <Grid2
            size={{ xs: 12, sm: 4, md: 2.6 }}
            className="flex w-[20%] h-full flex-col gap-y-5"
          >
            <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
              <PromotionState />
            </div>
            <PromotionActions
              onSubmit={methods.handleSubmit(handleSave)}
              isEdit={!!editPromotion}
            />
          </Grid2>
        </Grid2>
      </form>
    </FormProvider>
  );
};

export default PromotionForm;
