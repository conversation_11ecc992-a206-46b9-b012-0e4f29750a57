"use client";
import { Typography } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DemoContainer, DemoItem } from "@mui/x-date-pickers/internals/demo";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { es } from "date-fns/locale";

import styles from "./index.module.css";
import { useFormContext } from "react-hook-form";

const PromotionPeriod = () => {
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  
  // Observa los valores actuales de las fechas
  const startDate = watch("startDate") ?? null; // Asegúrate de que no sea undefined
  const endDate = watch("endDate") ?? null;

  const handleStartDateChange = (date: Date | null) => {
    setValue("startDate", date); // Actualiza el valor de startDate en el formulario
  };

  const handleEndDateChange = (date: Date | null) => {
    setValue("endDate", date); // Actualiza el valor de endDate en el formulario
  };
  return (
    <div className="flex flex-col w-full py-[12px] px-[10px] ">
      <Typography
        className={styles.informacinDeLa}
        variant="inherit"
        component="h3"
        sx={{ fontWeight: "500" }}
      >
        Período de promoción
      </Typography>

      <div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
          <DemoContainer components={["DatePicker"]}>
            <DemoItem
              label={
                <span
                  style={{
                    fontSize: "1.2rem",
                    fontWeight: "normal",
                    fontFamily: "Poppins",
                  }}
                >
                  Fecha de Inicio
                </span>
              }
            >
              <DatePicker
                label=""
                value={startDate}
                onChange={handleStartDateChange}
                slotProps={{
                  textField: {
                    error: !!errors.startDate,
                    helperText: errors.startDate?.message as string,
                    FormHelperTextProps: {
                      sx: {
                        color: "#d32f2f", // Color rojo para el mensaje de error
                        fontSize: "14px", // Tamaño de texto pequeño
                        fontFamily: "Poppins", // Fuente personalizada
                        marginTop: "4px", // Espaciado superior
                      },
                    },
                  },
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "20px", // Aquí ajustas el radio de los bordes
                  },
                }}
              />
            </DemoItem>

            <DemoItem
              label={
                <span
                  style={{
                    fontSize: "1.2rem",
                    fontWeight: "normal",
                    fontFamily: "Poppins",
                  }}
                >
                  Fecha de Finalización
                </span>
              }
            >
              <DatePicker
                label=""
                value={endDate} // Valor actual de endDate
                onChange={handleEndDateChange}
                slotProps={{
                  textField: {
                    error: !!errors.endDate,
                    helperText: errors.endDate?.message as string,
                    FormHelperTextProps: {
                      sx: {
                        color: "#d32f2f", // Color rojo para el mensaje de error
                        fontSize: "14px", // Tamaño de texto pequeño
                        fontFamily: "Poppins", // Fuente personalizada
                        marginTop: "4px", // Espaciado superior
                      },
                    },
                  },
                }}
              />
            </DemoItem>
          </DemoContainer>
        </LocalizationProvider>
      </div>
    </div>
  );
};

export default PromotionPeriod;
