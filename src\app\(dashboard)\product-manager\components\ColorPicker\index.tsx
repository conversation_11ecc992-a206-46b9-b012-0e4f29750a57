import React from "react";
import { ColorPicker } from "react-color-palette";
import "react-color-palette/css";
import "./color-picker.css";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import { hexToRgb, rgbToHsv } from "src/helper/colorUtils";

const ColorPalette = () => {
  const saveColor = useAddProductStore((state) => state.setColor);
  const color = useAddProductStore((state) => state.color);

  const presetColors = ["#89C598", "#51519B", "#AF8CC0", "#7ABBD4",];

  const buildColor = (hex: string) => {
    const rgb = hexToRgb(hex);
    const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b);
    return {
      hex,
      rgb: { ...rgb, a: 1 },
      hsv: { ...hsv, a: 1 },
    };
  };

  return (
    <div className="custom-layout">
      <ColorPicker color={color} onChange={saveColor} />

      <div className="flex w-full bg-white rounded-lg flex-col px-[10px] pb-3 gap-y-1 mt-5">
        <h4
          style={{
            fontFamily: "Poppins",
          }}
          className="text-left mt-4 text-[15px] font-poppins"
        >
          Colores guardados:
        </h4>
        <div className="flex flex-wrap justify-center gap-2 mt-4">
          {presetColors.map((preset) => (
            <div
              key={preset}
              onClick={() => saveColor(buildColor(preset))}
              style={{
                width: 30,
                height: 30,
                borderRadius: "50%",
                backgroundColor: preset,
                border:
                  preset.toLowerCase() === color.hex.toLowerCase()
                    ? "2px solid #555"
                    : "1px solid #ccc",
                cursor: "pointer",
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColorPalette;
