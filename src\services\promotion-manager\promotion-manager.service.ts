import { queryBuilder } from "src/helper/queryBuilder";
import { PromotionManagerTableResponseDto } from "./dtos/get-all-promotions-table-response.dto";
import { GetAllPromotionTableDto } from "./dtos/get-all-promotions-table.dto";
import { PROMOTION_MANAGER_ROUTES } from "src/configs/api-routes";
import { AppAlpexApiGateWay } from "../app.equinox.api-getway";
import { DuplicatePromotionResponse } from "./dtos/duplicate-promotion-response.dto";
import { GetPromotionByIdRes } from "./dtos/get-promotion-by-id";
import { CreatePromotionDto } from "./dtos/create-promotion.dto";
import { UpdatePromotionDto } from "./dtos/update-promotion.dto";
import { UpdateStatusPromotionDto } from "./dtos/update-status-promotion.dto";

class PromotionManagerService {
  public async getPromotionsPagination(
    params: GetAllPromotionTableDto,
    urlQ?: string
  ): Promise<PromotionManagerTableResponseDto> {
    try {
      const url = urlQ
        ? urlQ
        : queryBuilder(
            params.filters,
            `${PROMOTION_MANAGER_ROUTES.GET_ALL_PROMOTIONS_TABLE}`
          );

      const { data: response } =
        await AppAlpexApiGateWay.get<PromotionManagerTableResponseDto>(
          `${url}&currentPage=${params.pagination.currentPage}&pageSize=${params.pagination.pageSize}`
        );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async createPromotion(
    payload: CreatePromotionDto
  ): Promise<DuplicatePromotionResponse> {
    try {
      const { data: response } = await AppAlpexApiGateWay.post(
        `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}`,
        { ...payload }
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async duplicatePromotion(
    idPromotion: number
  ): Promise<DuplicatePromotionResponse> {
    try {
      const { data: response } = await AppAlpexApiGateWay.post(
        `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}/${idPromotion}/duplicate`
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async getPromotionById(
    idPromotion: string
  ): Promise<GetPromotionByIdRes> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get(
        `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}?id=${idPromotion}`
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  // public async updatePromotion(payload: UpdatePromotionDto) {
  //   try {
  //     const { data: response } = await AppAlpexApiGateWay.put(
  //       `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}`,
  //       { ...payload }
  //     );

  //     return response;
  //   } catch (error) {
  //     const errMessage = String(error);
  //     throw new Error(errMessage);
  //   }
  // }

  public async updatePromotion(payload: UpdatePromotionDto) {
    try {
      const { data: response } = await AppAlpexApiGateWay.put(
        `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}`,
        { ...payload }
      );
      return response;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // Si el error viene de Axios y tiene response.data.message
      if (error?.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      // Si no, el mensaje genérico
      throw new Error(error.message || "Error al actualizar la promoción.");
    }
  }

  public async updateStatusPromotion(payload: UpdateStatusPromotionDto) {
    try {
      const { data: response } = await AppAlpexApiGateWay.put(
        `${PROMOTION_MANAGER_ROUTES.PROMOTIONS}/update-status`,
        { ...payload }
      );
      return { message: response.message, data: response.data };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // Si el error viene de Axios y tiene response.data.message
      if (error?.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      // Si no, el mensaje genérico
      throw new Error(error.message || "Error al actualizar el estado.");
    }
  }
}

const promotionManagerService = new PromotionManagerService();
export default promotionManagerService;
