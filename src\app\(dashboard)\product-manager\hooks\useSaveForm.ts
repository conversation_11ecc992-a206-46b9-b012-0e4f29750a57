import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { toast } from "sonner";
import {
  AddProductForm,
  EditProductForm,
  PlanesProductos,
} from "../components/Form/interfaces/form.interface";
import { HookForm } from "src/app/interfaces/hook-form.interface";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import { useAddProduct } from "src/hooks/productManager/useAddProduct";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { useUpdateProduct } from "src/hooks/productManager/useUpdateProduct";
import { useDeleteProduct } from "src/hooks/productManager/useDeleteProduct";
import { ServerError } from "src/services/produc-manager/dtos/server-error.interface";
import { createPlans } from "src/services/produc-manager/plan.service";
import { useAddPlan } from "src/hooks/productManager/useAddPlan";
import { updatePlans } from "src/services/produc-manager/plan.service";
import { useUpdatePlan } from "src/hooks/productManager/useUpdatePlan";
import { useUploadFilePoliza } from "src/hooks/productManager/useUploadFilePoliza";
import { useUpdateFilePoliza } from "src/hooks/productManager/useUpdateFilePoliza";
import { useGetFilePoliza } from "src/hooks/productManager/useGetFilePoliza";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface ISaveFormProps {
  setValue: HookForm<AddProductForm>["setValue"];
}

const useSaveForm = () => {
  const { addProduct } = useAddProduct();
  const { updateProduct } = useUpdateProduct();
  const { deleteProduct } = useDeleteProduct();
  const { addPlan } = useAddPlan();
  const { updatePlan } = useUpdatePlan();
  const { uploadFilePoliza } = useUploadFilePoliza();
  const { updateFilePoliza } = useUpdateFilePoliza();
  const { getFilePoliza } = useGetFilePoliza();
  const router = useRouter();
  const { productId } = useParams();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onSaveForm = () => {};
  // const descriptionField = useAddProductStore((state) => state.description);
  const colorChoosed = useAddProductStore((state) => state.color);
  const responseFilesCoverage = useAddProductStore(
    (state) => state.responseFilesCoverage
  );

  const setFiles = useAddProductStore(
    (state) => state.setResponseFilesCoverage
  );
  const mainImageUrl = useAddProductStore((state) => state.mainImageUrl);

  const category = useAddProductStore((state) => state.selectedCategory);
  const categoryId = useAddProductStore((state) => state.selectedCategoryId); // Obtén el `category_id`
  const currentStatus = useAddProductStore((state) => state.status);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const setMainImageUrl = useAddProductStore(
    (state) => state.setAddProductForm
  );
  const originalPlanes = useAddProductStore((state) => state.originalPlanes);

  const onSubmit: SubmitHandler<AddProductForm | EditProductForm> = async (
    data
  ) => {
    try {
      if (productId) {
        // Edición de un producto
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { planesProductos, filePoliza, tipoReclamacionId, ...restData } =
          data;

        const editData: EditProductForm = {
          ...restData,
          mainImage: data.mainImage, // Asegúrate de que sea la URL generada
          secondaryImage: data.secondaryImage,
          type: category,
          category_id: categoryId,
          color: colorChoosed.hex,
          //Coberturas
          coberturas: data.coberturas.map((cobertura, index) => {
            const { icon, ...rest } = cobertura;
            const newIcon = responseFilesCoverage[index];
            const finalIcons =
              newIcon !== undefined ? newIcon : icon || { name: "", url: "" };

            const id = "id" in cobertura ? cobertura.id : index;

            return {
              id,
              ...rest,
              icon: finalIcons, // Asigna el nuevo objeto `icons`
            };
          }),
        };

        // Excluir el campo `status` si no fue modificado
        if (data.status === currentStatus) {
          delete editData.status;
        }

        //Si es de borrador a archivado y de publicado a Archivado, usar el delete del producto
        if (
          (currentStatus === "Borrador" && data.status === "Archivado") ||
          (currentStatus === "Publicado" && data.status === "Archivado")
        ) {
          delete editData.status;
          //Primero se actualiza el producto y luego se elimina para obtener status archivado
          const actualizar = await updateProduct(editData, productId);
          if (actualizar) {
            const res = await deleteProduct(Number(productId));
            if (res) {
              router.push("/product-manager");
            }
          }
        }

        const res = await updateProduct(editData, productId);

        if (filePoliza && filePoliza instanceof File) {
          const filePolizaExists = await getFilePoliza(Number(productId));
          if (filePolizaExists) {
            await updateFilePoliza(filePoliza, String(productId));
          } else {
            await uploadFilePoliza(filePoliza, String(productId));
          }
        }

        if (res && planesProductos) {
          // Actualiza los planes después de editar el producto
          await updatePlans(
            planesProductos,
            originalPlanes,
            Number(productId),
            addPlan,
            updatePlan
          );
          toast.success("Cambios guardados correctamente", {
            style: {
              fontFamily: "Poppins",
              fontWeight: "bold",
              color: "rgb(103 202 35)",
              background: "rgb(238 251 229)",
              border: "1px solid rgb(238 251 229)",
            },
          });
        }
      } else {
        // Creación de un nuevo producto
        const { planesProductos, filePoliza, ...restData } = data;
        const addData: AddProductForm = {
          ...restData,
          color: colorChoosed.hex,
          mainImage: mainImageUrl,
          secondaryImage: data.secondaryImage,
          type: category,
          tipoReclamacionId: 1,
          coberturas: data.coberturas.map((cobertura, index) => ({
            ...cobertura,
            icon: responseFilesCoverage[index],
            documentation: "1",
          })),
        };

        const res = await addProduct(addData);
        if (res) {
          if (filePoliza && filePoliza instanceof File) {
            await uploadFilePoliza(filePoliza, String(res.id));
          }
          await createPlans(
            planesProductos as PlanesProductos[],
            res.id,
            addPlan
          );
          setFiles([]);
          router.push("/product-manager");
        }
      }
    } catch (error: unknown) {
      toast.error("Error al guardar los cambios");
      // Verifica si el error tiene la estructura esperada
      if ((error as ServerError).message) {
        const serverError = error as ServerError;
        throw serverError.message;
      } else if (error instanceof Error) {
        throw error.message;
      } else {
        throw "Error desconocido al enviar el formulario";
      }
    }
  };

  return {
    onSubmit,
  };
};
export default useSaveForm;
