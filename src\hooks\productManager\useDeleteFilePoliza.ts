import { useState } from "react";
import productManagerService from "src/services/produc-manager/product-manager.service";

export function useDeleteFilePoliza() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteFilePoliza = async (productId: number) => {
    setLoading(true);
    setError(null);
    try {
      await productManagerService.deleteFilePoliza(productId);
      return true;
    } catch (err) {
      setError((err as Error).message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    deleteFilePoliza,
    loading,
    error,
  };
}
