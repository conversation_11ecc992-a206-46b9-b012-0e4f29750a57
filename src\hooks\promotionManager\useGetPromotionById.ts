import { useEffect, useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";
import { GetPromotionByIdRes } from "src/services/promotion-manager/dtos/get-promotion-by-id";

export const useGetPromotionById = (id: string) => {
  const [promotionById, setPromotionById] =
    useState<GetPromotionByIdRes | null>(null);

  useEffect(() => {
    const getPromotionById = async () => {
      try {
        if (id) {
          const response = await promotionManagerService.getPromotionById(id);
          setPromotionById(response);
        }
      } catch (error) {
        console.error(error);
      }
    };
    getPromotionById();
  }, [id]);
  return { promotionById };
};
