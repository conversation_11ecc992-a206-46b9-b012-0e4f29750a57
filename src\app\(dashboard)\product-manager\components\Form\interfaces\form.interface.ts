import { HookForm } from "src/app/interfaces/hook-form.interface";
import {
  FieldArrayWithId,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
} from "react-hook-form";

export interface InfoProductFormProps {
  hookForm: Partial<HookForm<AddProductForm>>;
  fields: FieldArrayWithId<
    AddProductForm,
    "planesProductos" | "coberturas",
    "id"
  >[];
  append: UseFieldArrayAppend<AddProductForm, "planesProductos" | "coberturas">;
  remove: UseFieldArrayRemove;
  descriptionProduct?: string;
  productId: number;
  mainImageEdit?: string;
  secondaryImageEdit?: string;
}

// export interface InfoProductFormProps {
//   hookForm: Partial<HookForm<AddProductForm>>;
//   fields: FieldArrayWithId<AddProductForm, "planesProductos", "id">[]; // Específico para planesProductos
//   append: UseFieldArrayAppend<AddProductForm, "planesProductos">;
//   remove: UseFieldArrayRemove;
//   descriptionProduct?: string;
//   productId: number;
//   mainImageEdit?: string;
//   secondaryImageEdit?: string;
// }

export interface FilesState {
  [key: string]: File[];
}

export interface FilesDto {
  name: string;
  type: string;
  base64: string;
}

export interface PlanesProductos {
  id?: number; // required
  id_plan?: number; // required
  insuredAmount: string; // required
  netPremium: string; // required
  deductible: string; // required
  iva: string; // required
  tieneIva?: boolean; // required
  total?: string; // required
  deletedAt?: string | null; // required
}

export interface CoveragesIcons {
  //nombre: string; // required
  //url: string; // required
  name?: string; // required
  url?: string; // required
}
export interface Coverages {
  name: string; // required
  icon: CoveragesIcons; // required
  percentageInsuredAmount: string; // required
  description: string; // required
  //icon?: CoveragesIcons; // required
}

export interface AddProductForm {
  // userId: number; // required
  name: string; // required
  idZurich?: number; // required
  type?: string; // required
  category_id: number; // required
  generalCoverage: string; // -> description (required)
  subtitle: string; // required
  status?: string; // required
  additionalDetails?: string | undefined;
  mainImage?: string; // required
  secondaryImage?: string;
  coverageName?: string;
  coverageDescription: string; // -> encabezado
  color?: string;
  planesProductos?: PlanesProductos[]; // required
  coberturas: Coverages[];
  filePoliza?: File | null;
  tipoReclamacionId?: number;
}

export interface EditProductForm extends AddProductForm {
  coberturas: CoveragesEdditProduct[]; // required
  //planesProductos?: InsuredAmountEditProduct[]; // required
}

export interface CoveragesEdditProduct extends Coverages {
  id: number; // required
  //icon?: CoveragesIcons;
}

export interface InsuredAmountEditProduct extends PlanesProductos {
  id: number; // required
}
