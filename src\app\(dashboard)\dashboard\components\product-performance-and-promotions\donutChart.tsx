import { Box, Typography } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Tooltip,
  ResponsiveContainer,
  PieLabelRenderProps,
} from "recharts";

const data = [
  { name: "Wiki Asalto", value: 837, color: "#a6a1e0" },
  { name: "Wiki Rodada", value: 456, color: "#a6dfff" },
  { name: "Wiki Gadget", value: 400, color: "#c4e1c1" },
  { name: "Wiki Phishing", value: 374, color: "#d4c3de" },
];

export default function DonutChart() {
  const renderLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    value,
  }: PieLabelRenderProps) => {
    const RADIAN = Math.PI / 180;
    const ir = Number(innerRadius ?? 0);
    const or = Number(outerRadius ?? 0);
    const x0 = Number(cx ?? 0);
    const y0 = Number(cy ?? 0);
    const radius = ir + (or - ir) * 0.7;
    const x = x0 + radius * Math.cos(-midAngle! * RADIAN);
    const y = y0 + radius * Math.sin(-midAngle! * RADIAN);

    return (
      <g transform={`translate(${x}, ${y})`}>
        <foreignObject x={-20} y={-12} width={55} height={40}>
          <div
            style={{
              background: "#eeeeee",
              borderRadius: "5px",
              fontSize: "16px",
              color: "#6D6D6D",
              padding: "2px 6px",
              textAlign: "center",
              fontWeight: 500,
            }}
          >
            {value}
          </div>
        </foreignObject>
      </g>
    );
  };

  return (
    <Box display="flex" alignItems="center" gap={3}>
      {/* Donut Chart a la izquierda */}
      <Box sx={{ width: 330, height: 260 }}>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              innerRadius={70}
              outerRadius={120}
              //paddingAngle={0}
              dataKey="value"
              labelLine={false}
              label={renderLabel}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </Box>

      <Box
        display="flex"
        flexDirection="column"
        gap={2}
        justifyContent="flex-start"
        height="100%"
        minHeight={260}
        py={3}
      >
        <Typography
          variant="h6"
          sx={{ mb: 5, color: "#6D6D6D", fontFamily: "Poppins" }}
        >
          Ventas por producto
        </Typography>
        <Box display="flex" flexDirection="column" gap={1}>
          {data.map((entry, index) => (
            <Box key={index} display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: "50%",
                  backgroundColor: entry.color,
                }}
              />
              <Typography
                variant="body2"
                sx={{ color: "#6D6D6D", fontFamily: "Poppins" }}
              >
                {entry.name}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}
