export interface DuplicatePromotionProduct {
  productId: number;
  insuredAmounts: number[];
}

export interface DuplicatePromotionData {
  name: string;
  description: string;
  typeDiscount: string;
  discount: number;
  startDate: string | null;
  endDate: string | null;
  code: string;
  uses: number;
  maxUses: number;
  status: string;
  userId: number;
  products: DuplicatePromotionProduct[];
  id: number;
}

export interface DuplicatePromotionResponse {
  status: number;
  message: string;
  data: DuplicatePromotionData;
}
