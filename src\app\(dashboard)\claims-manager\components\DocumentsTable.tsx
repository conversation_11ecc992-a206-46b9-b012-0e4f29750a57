"use client";

import DescriptionIcon from "@mui/icons-material/Description";
import { Checkbox, Button } from "@mui/material";

const documentos = [
  { nombre: "INE.pdf" },
  { nombre: "Comprobante.pdf" },
  { nombre: "Factura.pdf" },
];

export default function TablaDocumentos() {
  return (
    <div className="px-6 font-[Poppins] text-[#10265F] h-screen">
      {/* Botón descargar */}
      <div className="flex justify-start mb-4">
        <button className="border border-[#10265F] text-[#10265F] px-5 py-2 rounded-lg text-sm font-medium transition duration-150">
          <PERSON><PERSON>gar <PERSON>
        </button>
      </div>

      {/* Tabla */}
      <div className="w-full overflow-x-auto border-b border-gray-200">
        <table className="w-full text-sm border-collapse">
          <thead>
            <tr className="bg-[#575A6F1F] text-[#7D7D7D]">
              <th className="py-3 px-1 text-center font-bold">Documento</th>
              <th className="py-3 px-4 text-center font-bold border-l-2 border-gray-300">
                Aprobar
              </th>
              <th className="py-3 px-4 text-center font-bold border-l-2 border-gray-300">
                Rechazar
              </th>
              <th className="py-3 px-4 text-center font-bold border-l-2 border-gray-300">
                Nota
              </th>
            </tr>
          </thead>
          <tbody>
            {documentos.map((doc, idx) => (
              <tr
                key={idx}
                className="border-t border-gray-200 text-sm text-[#1F2937]"
              >
                {/* Documento */}
                <td className="px-1 py-4">
                  <div className="flex items-center border-2 border-[#10265F] rounded-md w-full min-h-[40px] relative max-w-xs mx-auto">
                    {/* Icono alineado a la izquierda */}
                    <span className="pl-3 flex items-center">
                      <DescriptionIcon
                        fontSize="small"
                        sx={{ color: "#7D7D7D" }}
                      />
                    </span>
                    {/* Nombre centrado */}
                    <div className="flex-1 flex justify-center">
                      <span className="text-center text-[#6D6D6D]">
                        {doc.nombre}
                      </span>
                    </div>
                  </div>
                </td>

                {/* Aprobar */}
                <td className="text-center">
                  <Checkbox
                    sx={{
                      color: "#00C853",
                      "&.Mui-checked": { color: "#00C853" },
                      padding: 0,
                    }}
                  />
                </td>

                {/* Rechazar */}
                <td className="text-center">
                  <Checkbox
                    sx={{
                      color: "#D50000",
                      "&.Mui-checked": { color: "#D50000" },
                      padding: 0,
                    }}
                  />
                </td>

                {/* Nota */}
                <td className="flex justify-center items-center py-5">
                  <div className="text-center align-middle bg-[#F0F0F0]">
                    <button className="border border-dashed border-[#828282] px-4 py-2 rounded-md flex items-center gap-2 hover:bg-gray-100 transition">
                      <DescriptionIcon
                        fontSize="small"
                        sx={{ color: "#7D7D7D" }}
                      />
                      <span className="text-sm font-medium text-[#6D6D6D]">
                        Nota
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Botón continuar */}
      <div className="flex justify-center mt-6">
        <Button
          variant="contained"
          disabled
          sx={{
            backgroundColor: "#10265F",
            marginTop: "25px",
            color: "white",
            borderRadius: "8px",
            px: 15,
            py: 1.3,
            textTransform: "none",
            fontFamily: "Poppins",
            fontWeight: 500,
            fontSize: "14px",
            boxShadow: "none",
            "&.Mui-disabled": {
              backgroundColor: "#EBEBEB",
              color: "#828282",
            },
          }}
        >
          Continuar
        </Button>
      </div>
    </div>
  );
}
