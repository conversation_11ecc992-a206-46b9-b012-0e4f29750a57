"use client";

import React from "react";
import { useRouter } from "next/navigation";
import ButtonMui from "src/app/components/ui/Button";
import TextMui from "src/app/components/ui/Text";
import Icon from "src/app/components/ui/icon";
import { dummyEndorsements } from "../../data/dummy-endorsements";
import { Endorsement } from "../../types/endorsement.types";

interface EndorsementDetailPageProps {
  params: Promise<{ endorsementId: string }>;
}

const EndorsementDetailPage = ({ params }: EndorsementDetailPageProps) => {
  const router = useRouter();
  const { endorsementId } = React.use(params);

  // Buscar el endoso por ID
  const endorsement = dummyEndorsements.find(e => e.id === endorsementId);

  const handleGoBack = () => {
    router.push("/endorsement-manager");
  };

  if (!endorsement) {
    return (
      <div className="flex h-full w-full flex-col bg-[#fff] rounded-[20.5px] p-6">
        <div className="flex items-center gap-4 mb-6">
          <ButtonMui
            textbutton=""
            variantstyle="secondary"
            startIcon={<Icon icon="tabler:arrow-back-up" />}
            onClick={handleGoBack}
            sx={{
              border: "none",
              boxShadow: "none",
              padding: 0,
              minWidth: "auto",
              background: "none",
              "&:hover": { background: "none" },
            }}
          />
          <TextMui text="Endoso no encontrado" type="title" className="font-bold" />
        </div>
      </div>
    );
  }

  // Función para renderizar el contenido según el estado
  const renderContent = () => {
    switch (endorsement.estatus) {
      case "En revisión":
        return <EnRevisionView endorsement={endorsement} />;
      case "Por actualizar":
        return <PorActualizarView endorsement={endorsement} />;
      case "Actualizado":
        return <ActualizadoView endorsement={endorsement} />;
      case "Cancelado":
        return <CanceladoView endorsement={endorsement} />;
      default:
        return <EnRevisionView endorsement={endorsement} />;
    }
  };

  return (
    <div className="flex h-full w-full flex-col bg-[#fff] rounded-[20.5px] p-6">
      {/* Header con botón de regreso */}
      <div className="flex items-center gap-4 mb-6">
        <ButtonMui
          textbutton=""
          variantstyle="secondary"
          startIcon={<Icon icon="tabler:arrow-back-up" />}
          onClick={handleGoBack}
          sx={{
            border: "none",
            boxShadow: "none",
            padding: 0,
            minWidth: "auto",
            background: "none",
            "&:hover": { background: "none" },
          }}
        />
      </div>

      {/* Contenido dinámico según el estado */}
      {renderContent()}
    </div>
  );
};

// Componente para vista "En revisión"
const EnRevisionView = ({ endorsement }: { endorsement: Endorsement }) => {
  return (
    <div className="space-y-6">
      {/* Título */}
      <TextMui text="Documentos" type="title" className="font-bold text-gray-800" />

      {/* Identificación endoso */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <TextMui text="Identificación endoso" type="subtitle" className="font-semibold text-gray-700 mb-3" />
        <div className="space-y-1 text-sm text-gray-600">
          <div><span className="font-medium">ID:</span> {endorsement.id}</div>
          <div><span className="font-medium">Fecha de solicitud:</span> 12/03/2025</div>
          <div><span className="font-medium">Nombre del usuario:</span> {endorsement.usuario}</div>
        </div>
      </div>

      {/* Cambio solicitado */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <TextMui text="Cambio solicitado" type="subtitle" className="font-semibold text-gray-700 mb-4" />

        {endorsement.tipoEndoso === "Cambio de domicilio" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <TextMui text="Cambio de Domicilio:" type="subtitle" className="font-medium text-gray-700 mb-2" />
              <ul className="text-sm text-gray-600 space-y-1">
                <li><span className="font-medium">Dirección anterior:</span> Calle Vieja 123, Col Antigua</li>
                <li><span className="font-medium">Nueva dirección:</span> Calle Vieja 123, Col Antigua</li>
              </ul>
            </div>
            <div>
              <TextMui text="Cambio de Nombre:" type="subtitle" className="font-medium text-gray-700 mb-2" />
              <ul className="text-sm text-gray-600 space-y-1">
                <li><span className="font-medium">Nombre anterior:</span> Juan Pérez Rodríguez</li>
                <li><span className="font-medium">Nuevo nombre:</span> Juan Pérez Torres</li>
              </ul>
            </div>
          </div>
        )}

        {endorsement.tipoEndoso === "Cambio de nombre" && (
          <div>
            <TextMui text="Cambio de Nombre:" type="subtitle" className="font-medium text-gray-700 mb-2" />
            <ul className="text-sm text-gray-600 space-y-1">
              <li><span className="font-medium">Nombre anterior:</span> Juan Pérez Rodríguez</li>
              <li><span className="font-medium">Nuevo nombre:</span> Juan Pérez Torres</li>
            </ul>
          </div>
        )}

        {endorsement.tipoEndoso === "Cancelación" && (
          <div>
            <TextMui text="Solicitud de Cancelación" type="subtitle" className="font-medium text-gray-700 mb-2" />
            <p className="text-sm text-gray-600">Cancelación completa de la póliza</p>
          </div>
        )}
      </div>

      {/* Revisión de documentos */}
      <div>
        <TextMui text="Revisión de documentos" type="subtitle" className="font-semibold text-gray-700 mb-4" />

        {/* Tabla de documentos */}
        <div className="bg-white border rounded-lg overflow-hidden">
          <div className="grid grid-cols-5 bg-gray-100 p-3 text-sm font-medium text-gray-700">
            <div>Archivo</div>
            <div>Manual</div>
            <div>Aprobar</div>
            <div>Rechazar</div>
            <div>Nota</div>
          </div>

          <div className="divide-y">
            <div className="grid grid-cols-5 p-3 items-center">
              <div className="flex items-center gap-2">
                <Icon icon="tabler:file-pdf" className="text-red-500" />
                <span className="text-sm">Comp_de domicilio.pdf</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="tabler:file-text" className="text-gray-500" />
                <span className="text-sm">Manual</span>
              </div>
              <div className="flex justify-center">
                <input type="checkbox" className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex justify-center">
                <input type="checkbox" className="w-4 h-4 text-red-600" />
              </div>
              <div className="flex justify-center">
                <button className="border border-dashed border-gray-300 px-3 py-1 rounded text-sm text-gray-500">
                  Nota
                </button>
              </div>
            </div>

            <div className="grid grid-cols-5 p-3 items-center">
              <div className="flex items-center gap-2">
                <Icon icon="tabler:file-pdf" className="text-red-500" />
                <span className="text-sm">Acta de nacimiento.pdf</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="tabler:file-text" className="text-gray-500" />
                <span className="text-sm">Manual</span>
              </div>
              <div className="flex justify-center">
                <input type="checkbox" className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex justify-center">
                <input type="checkbox" className="w-4 h-4 text-red-600" />
              </div>
              <div className="flex justify-center">
                <button className="border border-dashed border-gray-300 px-3 py-1 rounded text-sm text-gray-500">
                  Nota
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente para vista "Por actualizar"
const PorActualizarView = ({ endorsement }: { endorsement: Endorsement }) => {
  return (
    <div className="space-y-6">
      {/* Título */}
      <TextMui text="Actualizar endoso" type="title" className="font-bold text-gray-800" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Identificación endoso */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <TextMui text="Identificación endoso" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="space-y-1 text-sm text-gray-600">
            <div><span className="font-medium">ID:</span> {endorsement.id}</div>
            <div><span className="font-medium">Fecha de solicitud:</span> 12/03/2025</div>
            <div><span className="font-medium">Nombre del usuario:</span> {endorsement.usuario}</div>
          </div>
        </div>

        {/* Información adicional */}
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <TextMui text="Información adicional" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="tabler:alert-triangle" className="text-orange-500" />
            <span className="text-sm font-medium text-orange-700">Estatus: Por actualizar</span>
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-medium">Fecha de aprobación:</span> 05/06/2025
          </div>
        </div>
      </div>

      {/* Solicitud de cambio */}
      <div>
        <TextMui text="Solicitud de cambio de domicilio:" type="subtitle" className="font-semibold text-gray-700 mb-4" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Dirección anterior */}
          <div className="border border-orange-300 bg-orange-50 p-4 rounded-lg">
            <TextMui text="Dirección anterior:" type="subtitle" className="font-medium text-orange-700 mb-3" />
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Calle: <span className="font-medium text-orange-600">Nogal</span></li>
              <li>• Número ext: <span className="font-medium text-orange-600">45</span></li>
              <li>• Número int: <span className="font-medium text-orange-600">125</span></li>
              <li>• Colonia: <span className="font-medium text-orange-600">El mirador</span></li>
              <li>• Código postal: <span className="font-medium text-orange-600">4594</span></li>
              <li>• Estado: <span className="font-medium text-orange-600">Michoacán</span></li>
            </ul>
          </div>

          {/* Dirección nueva */}
          <div className="border border-blue-300 bg-blue-50 p-4 rounded-lg">
            <TextMui text="Dirección nueva:" type="subtitle" className="font-medium text-blue-700 mb-3" />
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Calle: <span className="font-medium text-blue-600">Av del Sol</span></li>
              <li>• Número ext: <span className="font-medium text-blue-600">55</span></li>
              <li>• Número int: <span className="font-medium text-blue-600">125</span></li>
              <li>• Colonia: <span className="font-medium text-blue-600">El mirador</span></li>
              <li>• Código postal: <span className="font-medium text-blue-600">4594</span></li>
              <li>• Estado: <span className="font-medium text-blue-600">Puebla</span></li>
            </ul>
          </div>
        </div>
      </div>

      {/* Botón de descarga */}
      <div className="flex justify-center">
        <button className="border border-dashed border-gray-400 px-6 py-3 rounded-lg text-gray-600 hover:bg-gray-50 flex items-center gap-2">
          <Icon icon="tabler:download" />
          <span>Subir contrato en PDF</span>
        </button>
      </div>

      {/* Botones de acción */}
      <div className="flex justify-center gap-4 pt-4">
        <ButtonMui
          textbutton="Guardar Cambios"
          variantstyle="secondary"
          sx={{
            backgroundColor: "#f3f4f6",
            color: "#374151",
            border: "1px solid #d1d5db",
            "&:hover": { backgroundColor: "#e5e7eb" }
          }}
        />
        <ButtonMui
          textbutton="Notificar Al Cliente"
          variantstyle="primary"
          sx={{
            backgroundColor: "#10265F",
            "&:hover": { backgroundColor: "#0f1f4f" }
          }}
        />
      </div>
    </div>
  );
};

// Componente para vista "Actualizado"
const ActualizadoView = ({ endorsement }: { endorsement: Endorsement }) => {
  return (
    <div className="space-y-6">
      {/* Título */}
      <TextMui text="Endoso actualizado" type="title" className="font-bold text-gray-800" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Identificación endoso */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <TextMui text="Identificación endoso" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="space-y-1 text-sm text-gray-600">
            <div><span className="font-medium">ID:</span> {endorsement.id}</div>
            <div><span className="font-medium">Fecha de solicitud:</span> 12/03/2025</div>
            <div><span className="font-medium">Nombre del usuario:</span> {endorsement.usuario}</div>
          </div>
        </div>

        {/* Información adicional */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <TextMui text="Información adicional" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="tabler:check-circle" className="text-green-500" />
            <span className="text-sm font-medium text-green-700">Estatus: Actualizado</span>
          </div>
          <div className="space-y-1 text-sm text-gray-600">
            <div><span className="font-medium">Fecha de aprobación:</span> 05/06/2025</div>
            <div><span className="font-medium">Fecha de solicitud:</span> 05/06/2025</div>
            <div><span className="font-medium">Fecha de notificación al cliente:</span> 05/06/2025</div>
            <div><span className="font-medium">Administrador que gestionó el endoso:</span> César Torres</div>
          </div>
        </div>
      </div>

      {/* Resumen de cambio */}
      <div>
        <TextMui text="Resumen de cambio de domicilio:" type="subtitle" className="font-semibold text-gray-700 mb-4" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Dirección anterior */}
          <div className="border border-orange-300 bg-orange-50 p-4 rounded-lg">
            <TextMui text="Dirección anterior:" type="subtitle" className="font-medium text-orange-700 mb-3" />
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Calle: <span className="font-medium text-orange-600">Nogal</span></li>
              <li>• Número ext: <span className="font-medium text-orange-600">45</span></li>
              <li>• Número int: <span className="font-medium text-orange-600">125</span></li>
              <li>• Colonia: <span className="font-medium text-orange-600">El mirador</span></li>
              <li>• Código postal: <span className="font-medium text-orange-600">4594</span></li>
              <li>• Estado: <span className="font-medium text-orange-600">Morelia</span></li>
            </ul>
          </div>

          {/* Dirección nueva */}
          <div className="border border-blue-300 bg-blue-50 p-4 rounded-lg">
            <TextMui text="Dirección nueva:" type="subtitle" className="font-medium text-blue-700 mb-3" />
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Calle: <span className="font-medium text-blue-600">Av del Sol</span></li>
              <li>• Número ext: <span className="font-medium text-blue-600">55</span></li>
              <li>• Número int: <span className="font-medium text-blue-600">125</span></li>
              <li>• Colonia: <span className="font-medium text-blue-600">El mirador</span></li>
              <li>• Código postal: <span className="font-medium text-blue-600">4594</span></li>
              <li>• Estado: <span className="font-medium text-blue-600">Puebla</span></li>
            </ul>
          </div>
        </div>
      </div>

      {/* Documento generado */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2 border border-gray-300 px-4 py-2 rounded-lg bg-white">
          <Icon icon="tabler:file-pdf" className="text-red-500" />
          <span className="text-sm text-gray-700">Contrato_archivo.pdf</span>
        </div>
      </div>
    </div>
  );
};

// Componente para vista "Cancelado"
const CanceladoView = ({ endorsement }: { endorsement: Endorsement }) => {
  return (
    <div className="space-y-6">
      {/* Título */}
      <TextMui text="En revisión - Cancelación de póliza" type="title" className="font-bold text-gray-800" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Resumen del usuario */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <TextMui text="Resumen del usuario" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="space-y-1 text-sm text-gray-600">
            <div><span className="font-medium">ID de póliza:</span> {endorsement.id}</div>
            <div><span className="font-medium">Fecha de solicitud:</span> 12/03/2025</div>
            <div><span className="font-medium">Nombre del usuario:</span> {endorsement.usuario}</div>
          </div>
        </div>

        {/* Resumen de la solicitud */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <TextMui text="Resumen de la solicitud" type="subtitle" className="font-semibold text-gray-700 mb-3" />
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="tabler:check-circle" className="text-green-500" />
            <span className="text-sm font-medium text-green-700">Estatus póliza: Activa</span>
          </div>
          <div className="text-sm text-gray-600">
            <div><span className="font-medium">Cambios solicitados:</span> cancelación completa</div>
          </div>
        </div>
      </div>

      {/* Botón de descarga */}
      <div className="flex justify-center">
        <button className="border border-dashed border-gray-400 px-6 py-3 rounded-lg text-gray-600 hover:bg-gray-50 flex items-center gap-2">
          <Icon icon="tabler:download" />
          <span>Subir documento PDF</span>
        </button>
      </div>

      {/* Botones de acción */}
      <div className="flex justify-center gap-4 pt-4">
        <ButtonMui
          textbutton="Detener Solicitud"
          variantstyle="secondary"
          sx={{
            backgroundColor: "#f3f4f6",
            color: "#374151",
            border: "1px solid #d1d5db",
            "&:hover": { backgroundColor: "#e5e7eb" }
          }}
        />
        <ButtonMui
          textbutton="Confirmar Cancelación"
          variantstyle="primary"
          sx={{
            backgroundColor: "#10265F",
            "&:hover": { backgroundColor: "#0f1f4f" }
          }}
        />
      </div>
    </div>
  );
};

export default EndorsementDetailPage;
