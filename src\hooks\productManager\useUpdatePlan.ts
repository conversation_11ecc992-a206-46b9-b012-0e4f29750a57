import { UpdatePlanDto } from "src/services/produc-manager/dtos/update-plan";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useUpdatePlan = () => {
  const updatePlan = async (id: number, params: UpdatePlanDto) => {
    try {
      const response = await productManagerService.updatePlan(params);
      return response;
    } catch (error) {
      console.error("Error al actualizar el plan:", error);
      throw error;
    }
  };

  return { updatePlan };
};
