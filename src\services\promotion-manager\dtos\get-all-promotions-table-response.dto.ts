import { PromotionManagerTablePaginationDto } from "./get-all-promotions-table.dto";

export interface Promotion {
  id: number;
  name: string;
  description: string;
  typeDiscount: string;
  discount: number;
  startDate: string;
  endDate: string;
  code: string;
  uses: number;
  maxUses: number;
  status: string;
}

export interface PromotionManagerTableResponseDto {
  data: Promotion[];
  pagination: PromotionManagerTablePaginationDto;
}
