/* eslint-disable @typescript-eslint/no-explicit-any */
import { EndorsementSearchResult } from "src/services/endorsement-manager/dtos/search-endorsements.dto";
import { Endorsement } from "../types/endorsement.types";

/**
 * Convierte los datos de la API al formato esperado por la tabla
 */
export const adaptEndorsementSearchResults = (
  apiResults: EndorsementSearchResult[]
): Endorsement[] => {
  return apiResults.map((result) => ({
    id: result.endorsementId,
    usuario: result.title, // Asumiendo que title contiene el nombre del usuario
    tipoEndoso: result.endorsementType as any, // Casting necesario por diferencias en tipos
    productoAsegurado: "Rodada" as any, // Valor por defecto, puede ajustarse según la API
    fechaRegistro: new Date().toLocaleDateString("es-ES", { 
      day: "2-digit", 
      month: "2-digit" 
    }), // Formato DD/MM
    estatus: result.status as any, // Casting necesario por diferencias en tipos
  }));
};

/**
 * Convierte los filtros de la tabla al formato esperado por la API
 */
export const adaptFiltersToApiParams = (filters: {
  status?: string | null;
  searchTerm?: string;
  order?: "ASC" | "DESC";
  tipoEndoso?: string | null;
}) => {
  const params: any = {};

  // Mapear status
  if (filters.status && filters.status !== "Todos") {
    params.status = filters.status;
  }

  // Mapear término de búsqueda
  if (filters.searchTerm) {
    // La API puede buscar por username o endorsementId
    // Intentamos determinar si es un ID (numérico) o un nombre
    if (/^\d+$/.test(filters.searchTerm)) {
      params.endorsementId = filters.searchTerm;
    } else {
      params.username = filters.searchTerm;
    }
  }

  // Mapear orden
  if (filters.order) {
    params.order = filters.order;
  }

  return params;
};
