/* eslint-disable @typescript-eslint/no-explicit-any */
import { EndorsementSearchResult } from "src/services/endorsement-manager/dtos/search-endorsements.dto";
import { Endorsement, EndorsementType } from "../types/endorsement.types";


// Mapea los tipos de endoso de la API a los tipos esperados por la aplicación
const mapEndorsementType = (apiType: string): EndorsementType => {
  const typeMap: Record<string, EndorsementType> = {
    "Cambio de domicilio": "Cambio de domicilio",
    Cancelación: "Cancelación",
    "Cambio de nombre": "Cambio de nombre",
  };

  return typeMap[apiType] || "Cambio de domicilio"; // Valor por defecto
};

// Convierte los datos de la API al formato esperado por la tabla
export const adaptEndorsementSearchResults = (
  apiResults: EndorsementSearchResult[]
): Endorsement[] => {
  return apiResults.map((result) => ({
    id: result.endorsementId.toString(),
    usuario: `${result.userId}`, // Generamos un nombre de usuario basado en el ID
    tipoEndoso: mapEndorsementType(result.tipo), // Mapear "Cambio de domicilio" a los tipos esperados
    fechaRegistro: new Date(result.fecha).toLocaleDateString("es-ES", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }), // Convertir fecha ISO a formato DD/MM
    estatus: result.estatus as any, // Usar el estatus directamente de la API
  }));
};

/**
 * Convierte los filtros de la tabla al formato esperado por la API
 */
export const adaptFiltersToApiParams = (filters: {
  status?: string | null;
  searchTerm?: string;
  order?: "ASC" | "DESC";
}) => {
  const params: any = {};

  // Mapear status - convertir múltiples estados separados por coma
  if (filters.status && filters.status !== "Todos") {
    // Si hay múltiples estados, la API podría necesitar un formato específico
    params.status = filters.status;
  }

  // Mapear término de búsqueda - solo para username
  if (filters.searchTerm) {
    params.username = filters.searchTerm;
  }

  // Mapear orden
  if (filters.order) {
    params.order = filters.order;
  }

  return params;
};
