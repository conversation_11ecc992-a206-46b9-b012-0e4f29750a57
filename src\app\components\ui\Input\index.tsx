import { styled, TextField, TextFieldProps } from "@mui/material";
import React from "react";

type InputMuiProps = TextFieldProps & {
	customstyle?: "custom";
};

const StyledInput = styled(TextField)<InputMuiProps>(({ customstyle }) => ({
	"& fieldset": { borderColor: "#afafaf" },
	"& .MuiInputBase-root": {
		fontFamily: "Poppins",
		fontSize: "14px",
		borderRadius: "18px",
	},
	"& .MuiOutlinedInput-root.Mui-focused  .MuiOutlinedInput-notchedOutline": {
		borderColor: "#10265F",
	},
	"& .MuiInputBase-input": { color: "#000000", fontSize: "14px" },
	"& .MuiInputLabel-root.Mui-focused": { color: "#10265F" },
	"& .MuiOutlinedInput-root": {
		"&:hover fieldset": {
			borderColor: "#10265F",
		},
	},
	...(customstyle === "custom" && {
		"& fieldset": { borderColor: "#EBEBEB" },
		"& .MuiOutlinedInput-root": {
			"&:hover fieldset": {
				borderColor: "#cccbcb",
			},
		},
		"& .MuiInputBase-root": {
			fontFamily: "Poppins",
			height: "45px",
			borderRadius: "8px",
		},
	}),
}));

const InputMui = ({ customstyle, ...props }: InputMuiProps) => {
	return (
		<StyledInput
			{...props}
			customstyle={customstyle}
			sx={{ fontSize: "14px" }}
		/>
	);
};

export default InputMui;
