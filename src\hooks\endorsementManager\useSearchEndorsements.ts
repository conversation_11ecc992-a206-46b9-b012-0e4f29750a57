import { useState } from "react";
import endorsementManagerService from "src/services/endorsement-manager/endorsement-manager.service";
import { 
  SearchEndorsementsDto, 
  EndorsementSearchResult 
} from "src/services/endorsement-manager/dtos/search-endorsements.dto";

export const useSearchEndorsements = () => {
  const [endorsements, setEndorsements] = useState<EndorsementSearchResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const searchEndorsements = async (params: SearchEndorsementsDto) => {
    try {
      setLoading(true);
      setError(null);
      const response = await endorsementManagerService.searchEndorsements(params);
      setEndorsements(response.data);
      return response.data;
    } catch (err) {
      console.error("Error al buscar endosos:", err);
      setError("No se pudieron buscar los endosos.");
      setEndorsements([]);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setEndorsements([]);
    setError(null);
  };

  return { 
    endorsements, 
    loading, 
    error, 
    searchEndorsements, 
    clearResults 
  };
};
