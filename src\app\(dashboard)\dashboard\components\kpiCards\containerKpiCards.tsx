import React from "react";
import KpiCard from "./kpiCard";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import PeopleIcon from "@mui/icons-material/PersonAdd";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import PercentIcon from "@mui/icons-material/Percent";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";

const KpiCards = () => (
  <div className="">
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 max-w-7xl w-full ">
      <KpiCard
        icon={<TrendingUpIcon />}
        title="Ventas"
        value={1500}
        description="Total de polizas contratadas en los últimos 30 días"
      />
      <KpiCard
        icon={<PeopleIcon />}
        title="Nuevos usuarios"
        value={120}
        description="Usuarios que se registraron o iniciaron sesión"
      />
      <KpiCard
        icon={<AttachMoneyIcon />}
        title="Ingresos"
        value={7500}
        valueSuffix="MXN"
        description="Ingresos netos por ventas"
      />

      <KpiCard
        icon={<PercentIcon />}
        title="Tasa de conversión"
        value={40}
        valueSuffix="%"
        description="Ingresos netos por ventas"
      />
      <KpiCard
        icon={<ShoppingCartIcon />}
        title="Tasa de abandono"
        value={75}
        valueSuffix="%"
        description="% de carritos no completados"
      />
    </div>
  </div>
);

export default KpiCards;
