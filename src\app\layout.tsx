import type { <PERSON>ada<PERSON> } from "next";
import { inter, poppins, mulish } from "../app/fonts/fonts";
import "./globals.css";
import { AuthProvider } from "src/context/login/AuthContext";
import { Toaster } from "sonner";
export const metadata: Metadata = {
	title: "Wiki",
	description: "Tu aliado en seguros",
	icons: [
		{
			rel: "icon",
			type: "image/png",
			sizes: "32x32",
			url: "/favicon.svg",
		},
	],
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body
				className={`${inter.variable} ${poppins.variable} ${mulish.variable} `}
				suppressHydrationWarning
			>
				<AuthProvider>
				<Toaster position="top-right" richColors />
					{children}
				</AuthProvider>
			</body>
		</html>
	);
}
