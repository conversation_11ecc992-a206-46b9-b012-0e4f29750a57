// export function toCustomISOString(date: Date): string {
//   const year = date.getFullYear();
//   const day = String(date.getDate()).padStart(2, "0");
//   const month = String(date.getMonth() + 1).padStart(2, "0");
//   // Mantén la hora en 00:00:00.000Z si solo quieres la fecha
//   return `${year}-${month}-${day}T00:00:00.000Z`;
// }

export function toCustomISOString(date: Date, useCurrentTime = false): string {
  if (useCurrentTime) {
    const now = new Date();
    date.setHours(
      now.getHours(),
      now.getMinutes(),
      now.getSeconds(),
      now.getMilliseconds()
    );
  }
  return date.toISOString();
}
