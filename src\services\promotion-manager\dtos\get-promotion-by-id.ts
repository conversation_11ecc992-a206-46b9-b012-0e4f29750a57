import { PromotionFormValues } from "src/app/(dashboard)/promotion-manager/components/Form/interfaces/form.interface";

export interface planesProducto {
  plan_id: number;
}

export interface PromotionProductFromApi {
  id: number;
  name: string;
  status: string;
  userId: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  id_zurich: number;
  type: string;
  cobertura_general: string;
  subtitulo: string;
  detalles_adicionales: string;
  imagen_principal: string;
  imagen_secundaria: string;
  descripcion_cobertura: string;
  color: string;
  nombre_cobertura: string;
  category_id: number;
  url: string | null;
  planes: {
    plan_id: number;
    tiene_iva: number;
    monto_asegurado: string;
  }[];
}

export interface GetPromotionByIdRes extends Omit<PromotionFormValues, "id"> {
  id: number;
}
