import { useState } from "react";
import miscManagerService from "src/services/misc-manager/misc-manager.service";
import {
  GetBannersResponse,
  CreateBannerPayload,
} from "src/services/misc-manager/dtos/banners.dto";

export const useCreateBanner = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createBanner = async (
    payload: CreateBannerPayload
  ): Promise<GetBannersResponse | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await miscManagerService.createBanner(payload);
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || "Error al crear el banner");
      } else {
        setError("Error al crear el banner");
      }
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { createBanner, loading, error };
};
