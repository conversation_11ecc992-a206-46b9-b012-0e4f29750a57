import { useState } from "react";
import productManagerService from "src/services/produc-manager/product-manager.service";

export function useUploadFilePoliza() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFilePoliza = async (file: File | null, productId: string) => {
    setLoading(true);
    setError(null);
    try {
      const url = await productManagerService.uploadFilePoliza(file, productId);
      return url;
    } catch (err) {
      setError((err as Error).message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    uploadFilePoliza,
    loading,
    error,
  };
}
