.informacinDeLa {
  margin: 0;
  align-self: stretch;
  position: relative;
}
.titulo {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  z-index: 1;
}
.nombreDeLa {
  margin: 0;
  position: relative;
}
.titulo1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}
.titulo2 {
  border: 0;
  background-color: transparent;
  align-self: stretch;
  font-family: Poppins;
  font-size: 16px;
  color: #afafaf;
}
.nombreDelProducto {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 6px;
}
.descripcinDeLa {
  margin: 0;
  flex: 1;
  position: relative;
}
.titulo3 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}
.component1,
.dropdown {
  font-family: Poppins;
  font-size: 16px;
  color: black;
}
.component1 {
  border: 1px solid #ebebeb;
  background-color: #fff;
  height: 90px;
  width: auto;
  outline: 0;
  align-self: stretch;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 10px 11px;
}
.dropdown {
  height: 44px;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
}
.descripcin {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.saveIcon,
.text {
  position: relative;
}
.saveIcon {
  width: 18px;
  height: 18px;
  overflow: hidden;
  flex-shrink: 0;
}
.text {
  letter-spacing: 1.25px;
  text-transform: capitalize;
  font-weight: 500;
}
.iconRight {
  width: 12px;
  position: relative;
  font-size: 16px;
  line-height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.button,
.content {
  flex-direction: row;
  align-items: center;
}
.content {
  display: flex;
  justify-content: flex-start;
  padding: 11px 0;
  gap: 11px;
}
.button {
  border-radius: 10px;
  background-color: #af8cc0;
  display: none;
  justify-content: center;
  padding: 0 20px;
  text-align: center;
  font-size: 14px;
  color: #fff;
}
.box,
.informacinDelProducto {
  align-self: stretch;
  flex-direction: column;
}
.box {
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #ebebeb;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 10px 11px;
  gap: 12px;
  z-index: 0;
  font-size: 22px;
}
.informacinDelProducto {
  border-bottom: 1px solid #ebebeb;
  justify-content: center;
  padding: 0 0 8px;
  gap: 4px;
  z-index: 2;
}
.dateContainer,
.informacinDelProducto,
.perodoDePromocin {
  display: flex;
  align-items: flex-start;
}
.dateContainer {
  border: 1px solid #ebebeb;
  background-color: #fff;
  height: 107px;
  width: auto;
  outline: 0;
  align-self: stretch;
  border-radius: 8px;
  box-sizing: border-box;
  flex-direction: row;
  justify-content: flex-start;
  padding: 12px;
  font-family: Poppins;
  font-size: 22px;
  color: #7d7d7d;
}
.perodoDePromocin {
  border-bottom: 1px solid #d9d9d9;
  flex-direction: column;
  padding: 0 0 8px;
  gap: 4px;
  z-index: 1;
}
.costos,
.perodoDePromocin,
.tabs {
  align-self: stretch;
  justify-content: flex-start;
}
.tabs {
  border: 1px solid #ebebeb;
  background-color: #fff;
  height: 111px;
  width: auto;
  outline: 0;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 12px 20px;
  font-family: Poppins;
  font-size: 22px;
  color: #7d7d7d;
}
.costos {
  border-bottom: 1px solid #d9d9d9;
  align-items: flex-start;
  padding: 0 0 8px;
  gap: 4px;
  z-index: 0;
}
.bloque,
.costos,
.informacin {
  display: flex;
  flex-direction: column;
}
.informacin {
  align-self: stretch;
  align-items: flex-end;
  justify-content: flex-start;
  gap: 29px;
  z-index: 0;
}
.bloque {
  flex: 1;
  border-radius: 8px;
  background-color: #fff;
  align-items: flex-start;
  justify-content: center;
  padding: 12px 10px;
  box-sizing: border-box;
  min-width: 700px;
  text-align: left;
  font-size: 24px;
  color: #7d7d7d;
  font-family: Poppins;
}
.estadoDeLa {
  margin: 0;
  align-self: stretch;
  position: relative;
}
.dropdown1 {
  align-self: stretch;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
  font-family: Poppins;
  font-size: 16px;
  color: #7d7d7d;
}
.informacinDelProducto1,
.promoStateContainer,
.titulo7 {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.titulo7 {
  justify-content: flex-start;
  gap: 4px;
}
.informacinDelProducto1,
.promoStateContainer {
  justify-content: center;
}
.informacinDelProducto1 {
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  box-sizing: border-box;
  padding: 10px 9px;
  min-width: 268px;
}
.promoStateContainer {
  z-index: 1;
}
.button1,
.content1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.content1 {
  justify-content: flex-start;
  padding: 11px 0;
  gap: 10px;
}
.button1 {
  border-radius: 10px;
  background-color: #10265f;
  justify-content: center;
  padding: 0 20px;
}
.buttonContainer,
.buttonContainerWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.buttonContainer {
  justify-content: center;
  min-width: 220px;
}
.buttonContainerWrapper {
  justify-content: flex-start;
  z-index: 0;
  text-align: center;
  font-size: 14px;
  color: #fff;
}
.promoStateContainerParent,
.root {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}
.promoStateContainerParent {
  width: 272px;
  flex-direction: column;
  min-width: 272px;
}
.root {
  width: 1380px;
  max-width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: flex-start;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  font-size: 22px;
  color: #7d7d7d;
  font-family: Poppins;
}
@media screen and (max-width: 1125px) {
  .bloque {
    width: calc(100% - 40px);
  }
}
@media screen and (max-width: 750px) {
  .bloque {
    min-width: 100%;
  }
}
@media screen and (max-width: 450px) {
  .informacinDeLa {
    font-size: 19px;
  }
  .estadoDeLa {
    font-size: 18px;
  }
}
