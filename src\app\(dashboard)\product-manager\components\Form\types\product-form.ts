import {
  AddProductForm,
  Coverages,
  PlanesProductos,
  CoveragesIcons,
} from "../interfaces/form.interface";

export const initialIconValues: CoveragesIcons = {
  name: "",
  url: "",
};
export const initialCoverageValues: Coverages = {
  name: "",
  percentageInsuredAmount: "",
  description: "",
  icon: initialIconValues,
};

export const initialPlanesProductosValues: PlanesProductos = {
  insuredAmount: "",
  netPremium: "",
  deductible: "",
  iva: "",
  total: "",
};

export const initialValues: AddProductForm = {
  // userId: 1,
  name: "",
  //idZurich: 0,
  //type: "",
  category_id: 0,
  generalCoverage: "",
  subtitle: "",
  status: "Borrador",
  additionalDetails: "",
  mainImage: "",
  secondaryImage: "",
  coverageName: "",
  coverageDescription: "",
  color: "",
  planesProductos: [initialPlanesProductosValues],
  coberturas: [initialCoverageValues],
  tipoReclamacionId: 0,
};
