"use client";

import { useEffect, useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";
import { Promotion } from "src/services/promotion-manager/dtos/get-all-promotions-table-response.dto";
import { GetAllPromotionTableDto } from "src/services/promotion-manager/dtos/get-all-promotions-table.dto";

const initialState: GetAllPromotionTableDto = {
  filters: [],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    totalRecords: 0,
  },
};

export const useGetPromotionsPagination = () => {
  const [promotionManagerPagination, setPromotionManagerPagination] =
    useState<GetAllPromotionTableDto>(initialState);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [pagination, setPagination] = useState(initialState.pagination);
  const [loading, setLoading] = useState<boolean>(true);

  const getPromotionsPagination = async (params: GetAllPromotionTableDto) => {
    const { data, pagination } =
      await promotionManagerService.getPromotionsPagination(params);
    setPromotions(data);
    setPagination(pagination);
    setLoading(false);

    return { promotions, pagination };
  };

  useEffect(() => {
    setLoading(true);
    if (promotionManagerPagination) {
      getPromotionsPagination(promotionManagerPagination);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionManagerPagination]);

  return {
    promotions,
    pagination,
    loading,
    promotionManagerPagination,
    setPromotionManagerPagination,
    setLoading,
    setPagination,
    getPromotionsPagination,
  };
};
