import { Select, SelectProps } from "@mui/material";
import React from "react";
import { poppins } from "src/app/fonts/fonts";

type SelectMuiProps = SelectProps & {
	children: React.ReactNode;
};

const SelectMui = ({ children, ...props }: SelectMuiProps) => {
	return (
		<Select
			{...props}
			sx={{
				height: "44px",
				fontSize: "14px",
				fontFamily: poppins.style.fontFamily,
				borderRadius: "8px",
				"&.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
					{
						borderColor:
							"#10265F" /* Cambia el color del borde cuando el componente está enfocado */,
					},
				"&.MuiOutlinedInput-root": {
					"&:hover fieldset": {
						borderColor: "#10265F", // Cambia el color del borde al pasar el mouse
					},
				},
			}}
		>
			{children}
		</Select>
	);
};

export default SelectMui;
