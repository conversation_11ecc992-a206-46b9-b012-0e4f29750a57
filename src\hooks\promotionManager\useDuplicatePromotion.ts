import { useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";

export const useDuplicatePromotion = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const duplicatePromotion = async (idPromotion: number) => {
    setLoading(true);
    setError(null);
    try {
      const response = await promotionManagerService.duplicatePromotion(
        idPromotion
      );
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Error duplicando promoción");
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { duplicatePromotion, loading, error };
};
