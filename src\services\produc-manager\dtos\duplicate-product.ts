import {
  AddProductForm,
  Coverages,
} from "src/app/(dashboard)/product-manager/components/Form/interfaces/form.interface";

export interface DuplicateProductResponseDto {
  message: string;
  duplicatedProduct: DuplicatedProduct[];
}

export interface DuplicatedProduct extends AddProductForm {
  id: number; // ID del producto duplicado
  createdAt: string; // Fecha de creación
  updatedAt: string; // Fecha de actualización
  deletedAt: string | null; // Fecha de eliminación (puede ser null)
  userId: number; // ID del usuario que duplicó el producto
  Coberturas: Coverages[]; // Parece ser un duplicado de `coberturas`
  user: {
    userId: number; // ID del usuario
  };
}
