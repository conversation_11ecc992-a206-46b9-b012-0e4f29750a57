"use client";
import { Box, TextField, MenuItem, SelectChangeEvent } from "@mui/material";
import styles from "./index.module.css";
import TextMui from "src/app/components/ui/Text";
import SelectMui from "src/app/components/ui/Select";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useFormContext } from "react-hook-form";
import { useGetProductsActive } from "src/hooks/productManager/useGetProductsActive";
import { PromotionFormValues } from "./interfaces/form.interface";

const PromotionInfo = () => {
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext(); // Conecta al formulario principal

  const { products: activeProducts } = useGetProductsActive();

  const products = (activeProducts ?? []).map((product) => ({
    label: product.name,
    value: String(product.id),
  }));

  const selectedProducts = (
    (watch("products") as PromotionFormValues["products"]) || []
  ).map((p) => String(p.productId));

  const handleProductChange = (event: SelectChangeEvent<unknown>) => {
    const value = event.target.value as string[]; // IDs seleccionados

    const productsArray = value.map((id) => {
      // Busca el producto activo correspondiente
      const foundProduct = activeProducts?.find((p) => String(p.id) === id);

      // Obtén todos los ids de planesProductos
      const planesIds =
        foundProduct?.planesProductos?.map((plan) => plan.id) || [];

      // Devuelve el objeto completo para el formulario
      return {
        productId: Number(id),
        insuredAmounts: planesIds,
      };
    });

    setValue("products", productsArray);
  };

  return (
    <div className="flex flex-col w-full py-[12px] px-[10px] ">
      <TextMui
        text="Información de la promoción"
        type="medium"
        className="text-[#333333]"
      />
      <div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
        <Box className={styles.nombreDelProducto}>
          <TextMui
            text="Nombre de la promoción*"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />
          <TextField
            {...register("name")} // Registra el campo en react-hook-form
            className={styles.titulo2}
            placeholder="Máximo 50 caracteres"
            variant="outlined"
            error={!!errors.name} // Marca el campo como error si hay un error
            helperText={errors.name?.message as string}
            FormHelperTextProps={{
              sx: {
                color: "#d32f2f", // Cambia el color del mensaje de error
                fontSize: "14px", // Tamaño de texto pequeño
                fontFamily: "Poppins", // Fuente personalizada
                marginTop: "4px", // Espaciado superior
              },
            }}
            sx={{
              "& fieldset": { borderColor: "#ebebeb" },
              "& .MuiInputBase-root": {
                height: "48px",
                backgroundColor: "#fff",
                borderRadius: "8px",
              },
              //"& .MuiInputBase-input": { color: "#afafaf" },
            }}
          />
        </Box>
        <Box className={styles.nombreDelProducto}>
          <TextMui
            text="Descripción de la promoción*"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />
          <textarea
            {...register("description")}
            className={`${styles.component1} ${
              errors.description ? "border-red-500" : ""
            }`}
            placeholder="150 caracteres máximo"
            rows={5}
            cols={52}
          />
          {errors.description && (
            <p className="text-[#d32f2f] text-sm">
              {errors.description.message as string}
            </p>
          )}
        </Box>
        <Box className={`${styles.nombreDelProducto} w-1/3`}>
          <TextMui
            text="Código de la promoción*"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />
          <TextField
            {...register("code")} // Registra el campo en react-hook-form
            className={styles.titulo2}
            placeholder="Máximo 10 caracteres"
            variant="outlined"
            error={!!errors.code} // Marca el campo como error si hay un error
            helperText={errors.code?.message as string}
            FormHelperTextProps={{
              sx: {
                color: "#d32f2f", // Cambia el color del mensaje de error
                fontSize: "14px", // Tamaño de texto pequeño
                fontFamily: "Poppins", // Fuente personalizada
                marginTop: "4px", // Espaciado superior
              },
            }}
            sx={{
              "& fieldset": { borderColor: "#ebebeb" },
              "& .MuiInputBase-root": {
                height: "48px",
                backgroundColor: "#fff",
                borderRadius: "8px",
              },
            }}
          />
        </Box>
        <Box className={`${styles.nombreDelProducto} w-1/3`}>
          <TextMui
            text="Máximo de usos*"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />
          <TextField
            {...register("maxUses")}
            type="number"
            className={styles.titulo2}
            placeholder="Escribe el número máximo de usos"
            variant="outlined"
            error={!!errors.maxUses}
            helperText={errors.maxUses?.message as string}
            FormHelperTextProps={{
              sx: {
                color: "#d32f2f",
                fontSize: "14px",
                fontFamily: "Poppins",
                marginTop: "4px",
              },
            }}
            sx={{
              "& fieldset": { borderColor: "#ebebeb" },
              "& .MuiInputBase-root": {
                height: "48px",
                backgroundColor: "#fff",
                borderRadius: "8px",
              },
            }}
          />
        </Box>
        <div className="flex max-w-[400px] bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
          <TextMui
            text="Producto asociado*"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />

          <SelectMui
            multiple // Permite seleccionar múltiples opciones
            value={selectedProducts} // Array de ids seleccionados
            onChange={handleProductChange} // Maneja el cambio
            fullWidth
            displayEmpty
            IconComponent={ExpandMoreIcon}
            error={!!errors.products} // Marca el campo como error si hay un error
            renderValue={(selected) =>
              (selected as string[]).length === 0
                ? "Nombre del producto"
                : (selected as string[])
                    .map(
                      (id) =>
                        products.find((product) => product.value === id)?.label
                    )
                    .join(", ")
            } // Muestra los nombres de los productos seleccionados
          >
            <MenuItem disabled value="">
              Seleccione productos
            </MenuItem>
            {products.map((product) => (
              <MenuItem
                key={product.value}
                value={product.value}
                sx={{ fontFamily: "Poppins" }}
              >
                {product.label}
              </MenuItem>
            ))}
          </SelectMui>
          {errors.products && (
            <p
              style={{
                color: "#d32f2f",
                fontSize: "14px",
                fontFamily: "Poppins",
                marginTop: "4px",
              }}
            >
              {errors.products.message as string}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PromotionInfo;
