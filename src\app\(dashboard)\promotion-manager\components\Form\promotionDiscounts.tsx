"use client";
import { InputAdornment, MenuItem, SelectChangeEvent } from "@mui/material";
import SelectMui from "src/app/components/ui/Select";
import TextMui from "src/app/components/ui/Text";
import InputMui from "src/app/components/ui/Input";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { NumericFormat } from "react-number-format";
import { useFormContext } from "react-hook-form";

const PromotionDiscounts = () => {
  const {
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const tipoDeDescuento = ["Porcentaje", "Monto fijo"];

  // Observa los valores actuales del formulario
  const discountType = watch("typeDiscount") || ""; // Tipo de descuento seleccionado
  const discountValue = watch("discount") || "";

  const handleDiscountTypeChange = (event: SelectChangeEvent<unknown>) => {
    setValue("typeDiscount", event.target.value); // Actualiza el tipo de descuento
  };

  const handleDiscountValueChange = (value: string) => {
    const numericValue = value === "" ? null : parseFloat(value);
    setValue("discount", numericValue); // Actualiza el valor del descuento
  };

  return (
    <div className="flex flex-col w-full py-[12px] px-[10px] ">
      <TextMui
        text="Descuentos"
        type="medium"
        className="text-[#7D7D7D] font-medium mb-2"
      />
      <div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
        <div className="flex max-w-[550px] bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
          <TextMui
            text="Tipo de descuento"
            type="inputLabel"
            className="text-[#7D7D7D]"
          />
          <div className="flex w-full flex-row gap-x-[12px]">
            <div className="flex w-full flex-col gap-y-[10px]">
              <SelectMui
                value={discountType}
                onChange={handleDiscountTypeChange}
                fullWidth
                IconComponent={ExpandMoreIcon}
                error={!!errors.typeDiscount}
                displayEmpty
                renderValue={(selected) =>
                  !selected ? "Tipo de descuento" : (selected as string)
                }
              >
                <MenuItem disabled value="">
                  Tipo de descuento
                </MenuItem>
                {tipoDeDescuento.map((item, index) => (
                  <MenuItem
                    key={index}
                    value={item}
                    sx={{ fontFamily: "Poppins" }}
                  >
                    {item}
                  </MenuItem>
                ))}
              </SelectMui>
              {errors.typeDiscount && (
                <p className="text-[#d32f2f] text-sm">
                  {errors.typeDiscount.message as string}
                </p>
              )}
            </div>

            <div className="flex w-full flex-col gap-y-[10px] justify-end">
              <NumericFormat
                //value={0}
                value={discountValue}
                onValueChange={(values) =>
                  handleDiscountValueChange(values.value)
                }
                //onBlur={}
                decimalScale={2}
                allowNegative={false}
                customstyle="custom"
                customInput={InputMui}
                thousandSeparator=","
                decimalSeparator="."
                placeholder="0.00"
                error={!!errors.discount}
                //isAllowed={}
                slotProps={{
                  formHelperText: {
                    sx: { fontFamily: "Poppins", ml: 0 },
                  },
                  input: {
                    endAdornment: (
                      <InputAdornment position="start">%</InputAdornment>
                    ),
                  },
                }}
              />
              {errors.discount && (
                <p className="text-[#d32f2f] text-sm">
                  {errors.discount.message as string}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionDiscounts;
