name: wikiCMS CI/CD
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.17.0]

    steps:
      - uses: actions/checkout@v4
      - name: Clean folder
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: app
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            rm -Rf /home/<USER>/wikiCMS
            mkdir /home/<USER>/wikiCMS
            chmod -Rf 777 /home/<USER>/wikiCMS
      - name: rsync deployments
        uses: burnett01/rsync-deployments@5.1
        with:
          switches: -avzr --delete
          path: ./*
          remote_path: /home/<USER>/wikiCMS/
          remote_host: *************
          remote_user: app
          remote_key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'

      - name: deploy to server and application start
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: app
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            export PATH=$HOME/.npm-global/bin:$PATH
            cd /home/<USER>/wikiCMS/ && npm install
            echo ${{ vars.WikiBack_ENV }} | base64 --decode --ignore-garbage > .env
            cd /home/<USER>/wikiCMS/ && npm run build
            pm2 resurrect
            cd /home/<USER>/wikiCMS/ && pm2 stop wikiCMS && pm2 delete wikiCMS && pm2 start npm --name "wikiCMS" -- start -- --port 7000
