"use client";
import React, { useEffect, useRef, useState } from "react";
import { Controller } from "react-hook-form";
import InputMui from "src/app/components/ui/Input";
import TextMui from "src/app/components/ui/Text";
import { FilesState, InfoProductFormProps } from "./interfaces/form.interface";
import LoadFileButton from "src/app/components/custom/LoadFileButton";
import Icon from "src/app/components/ui/icon";
import { acceptedFileTypes } from "../../lib/utils";
import { useUploadImagesAzure } from "src/hooks/productManager/useUploadImagesAzure";
import { FormHelperText, IconButton } from "@mui/material";
import dynamic from "next/dynamic";
import Quill from "quill";
import { useAddProductStore } from "src/store/zustand/addProduct/add-product.store";
import ButtonMui from "src/app/components/ui/Button";
import IconifyIcon from "src/app/components/ui/icon";
import { useAddProductForm } from "../../hooks/useCustomForm";
import ConfirmationDialog from "src/app/components/ui/toast/ConfirmationDialog";

const TextEditor = dynamic(() => import("../QuillEditor"), { ssr: false });

const InfoProductForm = ({
  hookForm,
  descriptionProduct,
  productId,
  mainImageEdit,
  secondaryImageEdit,
}: Partial<InfoProductFormProps>) => {
  const { uploadImagesAzure, deleteImagesAzure } = useUploadImagesAzure();
  const setMainImageUrl = useAddProductStore(
    (state) => state.setAddProductForm
  );

  const { handleClickSave } = useAddProductForm();

  const quillRef = useRef<Quill | null>(null);
  const uploadFilesRef = useRef<HTMLInputElement | null>(null);
  const uploadFilesSecondaryRef = useRef<HTMLInputElement | null>(null);
  const [filesToUpload, setFilesToUpload] = useState<FilesState>({});
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [message, setMessage] = useState<boolean>(false);
  const [confirmDelete, setConfirmDelete] = useState<{
    key: string;
    show: boolean;
  }>({ key: "", show: false });

  const formIconsData = new FormData();

  // Desestructurar los archivos de filesToUpload
  const { uploadMainImage = [], uploadSecondaryImage = [] } = filesToUpload;
  const [mainImageLoaded] = uploadMainImage;
  const [secondaryImageLoaded] = uploadSecondaryImage;
  const [uploadedFiles, setUploadedFiles] = useState<Set<string>>(new Set());
  const [localMainImageEdit, setLocalMainImageEdit] = useState(mainImageEdit);
  const [localSecondaryImageEdit, setLocalSecondaryImageEdit] =
    useState(secondaryImageEdit);

  const extractImageName = (url: string): string | null => {
    const match = url.match(/\/([^\/?]+)\?/); // Busca el nombre del archivo entre "/" y "?"
    return match ? match[1] : null; // Retorna el nombre si se encuentra, de lo contrario null
  };
  // const mainImageName = mainImageEdit
  // 	? extractImageName(mainImageEdit)
  // 	: null;
  //console.log("mainName",mainImageName)
  if (mainImageEdit?.startsWith("L")) {
    mainImageEdit = "";
  }
  if (secondaryImageEdit?.startsWith("L")) {
    secondaryImageEdit = "";
  }
  const textWithoutStyles = quillRef?.current?.getText().trim();

  const handleFileLoaded = (key: string) => (files: File | File[]) => {
    setFilesToUpload((prevFiles: FilesState) => {
      // Convertimos 'files' en un arreglo si no lo es
      const newFiles = Array.isArray(files) ? files : [files];

      // Obtenemos los archivos existentes para la clave, si no hay, usamos un arreglo vacío
      const existingFiles = prevFiles[key] || [];

      // Actualizamos el estado combinando los archivos existentes con los nuevos
      const updatedFiles = [...existingFiles, ...newFiles];

      return { ...prevFiles, [key]: updatedFiles };
    });
  };

  const handleDeleteLocalFile = (key: string) => () => {
    console.log("handleDeleteLocalFile", formIconsData);
    setConfirmDelete({ key, show: true }); // Muestra el cuadro de confirmación
  };

  const handleConfirmDelete = async (key: string) => {
    const imageUrl =
      key === "uploadMainImage" ? localMainImageEdit : localSecondaryImageEdit;
    const imageName = imageUrl ? extractImageName(imageUrl) : null;

    if (!imageName) {
      console.error("No se pudo obtener el identificador de la imagen.");
      return;
    }

    try {
      const response = await deleteImagesAzure(imageName);
      if (!response) {
        throw new Error("Error al borrar la imagen en Azure.");
      }

      console.log(`Imagen ${imageName} eliminada correctamente de Azure.`);
    } catch (error) {
      console.error("Error al borrar la imagen:", error);
    }

    // Limpia el estado local y permite cargar una nueva imagen
    setFilesToUpload((prevFiles: FilesState) => ({
      ...prevFiles,
      [key]: [],
    }));

    // Limpia el formulario controlado por react-hook-form
    if (key === "uploadMainImage") {
      hookForm?.setValue?.("mainImage", ""); // Limpia el valor del formulario
      setMainImageUrl({ mainImageUrl: "" }); // Limpia el estado global (si aplica)
      setLocalMainImageEdit(""); // Limpia el estado local
    } else if (key === "uploadSecondaryImage") {
      hookForm?.setValue?.("secondaryImage", ""); // Limpia el valor del formulario
      setLocalSecondaryImageEdit(""); // Limpia el estado local
    }
    // Cerrar el cuadro de confirmación
    setConfirmDelete({ key: "", show: false });
  };

  const handleIconsLoaded = async (key: string) => {
    if (key === "uploadMainImage") {
      formIconsData.append("file", mainImageLoaded);
      formIconsData.append("name", mainImageLoaded.name);
    } else if (key === "uploadSecondaryImage") {
      formIconsData.append("file", secondaryImageLoaded);
      formIconsData.append("name", secondaryImageLoaded.name);
    }

    const response = await uploadImagesAzure(formIconsData);

    // Verifica que la respuesta contenga una URL válida
    if (response?.url) {
      if (key === "uploadMainImage") {
        setMainImageUrl({ mainImageUrl: response.url }); // Actualiza el estado global
        hookForm?.setValue?.("mainImage", response.url); // Actualiza el formulario
      } else if (key === "uploadSecondaryImage") {
        hookForm?.setValue?.("secondaryImage", response.url); // Actualiza el formulario
      }
    } else {
      console.error("Error al subir la imagen: No se recibió una URL válida.");
    }
    setUploadedFiles((prev) => new Set(prev.add(key)));
  };

  useEffect(() => {
    if (mainImageLoaded && !uploadedFiles.has("uploadMainImage")) {
      handleIconsLoaded("uploadMainImage");
    } else if (
      secondaryImageLoaded &&
      !uploadedFiles.has("uploadSecondaryImage")
    ) {
      handleIconsLoaded("uploadSecondaryImage");
    }
    return;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filesToUpload]);

  useEffect(() => {
    if (textWithoutStyles === "") {
      hookForm?.resetField?.("generalCoverage");
    }
    return;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [textWithoutStyles]);

  useEffect(() => {
    if (mainImageLoaded) {
      hookForm?.setValue?.("mainImage", mainImageLoaded?.name);
    } else {
      hookForm?.setValue?.("mainImage", "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainImageLoaded]);

  useEffect(() => {
    setLocalMainImageEdit(mainImageEdit);
  }, [mainImageEdit]);

  useEffect(() => {
    setLocalSecondaryImageEdit(secondaryImageEdit);
  }, [secondaryImageEdit]);

  return (
    <div className="flex flex-col w-full py-[12px] px-[10px] ">
      <div className="flex w-full flex-col gap-y-[4px]">
        <TextMui
          text="Información del producto"
          type="medium"
          className="text-[#333333]"
        />
        <div className="flex w-full flex-col border rounded-lg border-[#EBEBEB] p-[12px] gap-y-[12px]">
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Nombre del producto*" type="inputLabel" />
            <Controller
              name="name"
              defaultValue=""
              control={hookForm?.control}
              render={({ field: { value, onChange, onBlur } }) => (
                <InputMui
                  fullWidth
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  placeholder="Ingresa el nombre del producto"
                  customstyle="custom"
                  error={!!hookForm?.errors?.name}
                  helperText={hookForm?.errors?.name?.message}
                  slotProps={{
                    formHelperText: {
                      sx: { fontFamily: "Poppins", ml: 0 },
                    },
                  }}
                />
              )}
            />
          </div>
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Nombre Producto APP*" type="inputLabel" />
            <Controller
              name="coverageName"
              control={hookForm?.control}
              render={({ field: { value, onChange, onBlur } }) => (
                <InputMui
                  fullWidth
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  placeholder="Ingresa el nombre del producto para la APP"
                  customstyle="custom"
                  error={!!hookForm?.errors?.coverageName}
                  helperText={hookForm?.errors?.coverageName?.message}
                  slotProps={{
                    formHelperText: {
                      sx: { fontFamily: "Poppins", ml: 0 },
                    },
                  }}
                />
              )}
            />
          </div>
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Encabezado*" type="inputLabel" />
            <Controller
              name="coverageDescription"
              control={hookForm?.control}
              render={({ field: { value, onChange, onBlur } }) => (
                <InputMui
                  fullWidth
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  placeholder="Ingresa el encabezado"
                  customstyle="custom"
                  error={!!hookForm?.errors?.coverageDescription}
                  helperText={hookForm?.errors?.coverageDescription?.message}
                  slotProps={{
                    formHelperText: {
                      sx: { fontFamily: "Poppins", ml: 0 },
                    },
                  }}
                />
              )}
            />
          </div>
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Subtitulo*" type="inputLabel" />
            <Controller
              name="subtitle"
              control={hookForm?.control}
              render={({ field: { value, onChange, onBlur } }) => (
                <InputMui
                  fullWidth
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  placeholder="Ingresa el subtitulo"
                  customstyle="custom"
                  error={!!hookForm?.errors?.subtitle}
                  helperText={hookForm?.errors?.subtitle?.message}
                  slotProps={{
                    formHelperText: {
                      sx: { fontFamily: "Poppins", ml: 0 },
                    },
                  }}
                />
              )}
            />
          </div>
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Imagen" type="inputLabel" />
            <div className="flex w-full h-full flex-col border rounded-lg border-[#EBEBEB] p-3 gap-y-3">
              <div className="flex w-full flex-col">
                <div className="flex w-full min-h-[78px] border border-dashed items-center justify-between p-5 ">
                  {productId && localMainImageEdit ? (
                    <div className="flex w-full items-center justify-center">
                      <TextMui
                        text={"Imagen cargada"}
                        type="subtitle"
                        className="pr-2"
                      />
                      <IconButton
                        onClick={handleDeleteLocalFile("uploadMainImage")}
                      >
                        <Icon icon={"mynaui:trash"} color="#10265F" />
                      </IconButton>
                    </div>
                  ) : mainImageLoaded ? (
                    <div className="flex w-full items-center justify-center">
                      <TextMui
                        text={mainImageLoaded.name}
                        type="subtitle"
                        className="pr-2"
                      />
                      <IconButton
                        onClick={handleDeleteLocalFile("uploadMainImage")}
                      >
                        <Icon icon={"mynaui:trash"} color="#10265F" />
                      </IconButton>
                    </div>
                  ) : (
                    <>
                      <div className="flex w-full items-center justify-center">
                        <Icon icon={"carbon:image"} color="#AFAFAF" />
                        <TextMui
                          text="Agregar Imagen* SVG (Recomendado 121x135 px)"
                          type="subtitle"
                          className="pl-2"
                        />
                      </div>
                      <Controller
                        name="mainImage"
                        control={hookForm?.control}
                        render={() => (
                          <LoadFileButton
                            title="Subir"
                            inputRef={uploadFilesRef}
                            onFileLoaded={handleFileLoaded("uploadMainImage")}
                            acceptedFiles={acceptedFileTypes}
                            maxSizeInMB={20}
                            maxFiles={1}
                            setMessage={setMessage}
                            dimensions={{ width: 121, height: 135 }}
                            sx={{ minWidth: "105px" }}
                          />
                        )}
                      />
                    </>
                  )}
                </div>
                <FormHelperText
                  error={!!hookForm?.errors?.mainImage}
                  sx={{ fontFamily: "Poppins", ml: 0 }}
                >
                  {!mainImageLoaded && hookForm?.errors?.mainImage?.message}
                </FormHelperText>
              </div>
              <div className="flex w-full min-h-[78px] border border-dashed items-center justify-between p-5 ">
                {productId && localSecondaryImageEdit ? (
                  <div className="flex w-full items-center justify-center">
                    <TextMui
                      text={"Imagen cargada"}
                      type="subtitle"
                      className="pr-2"
                    />
                    <IconButton
                      onClick={handleDeleteLocalFile("uploadSecondaryImage")}
                    >
                      <Icon icon={"mynaui:trash"} color="#10265F" />
                    </IconButton>
                  </div>
                ) : secondaryImageLoaded ? (
                  <div className="flex w-full items-center justify-center">
                    <TextMui
                      text={secondaryImageLoaded.name}
                      type="subtitle"
                      className="pr-2"
                    />
                    <IconButton
                      onClick={handleDeleteLocalFile("uploadSecondaryImage")}
                    >
                      <Icon icon={"mynaui:trash"} color="#10265F" />
                    </IconButton>
                  </div>
                ) : (
                  <>
                    <div className="flex w-full items-center justify-center">
                      <Icon icon={"carbon:image"} color="#AFAFAF" />
                      <TextMui
                        text="Agregar Imagen* SVG (Recomendado 143x160px)"
                        type="subtitle"
                        className="pl-2"
                      />
                    </div>
                    <LoadFileButton
                      title="Subir"
                      inputRef={uploadFilesSecondaryRef}
                      onFileLoaded={handleFileLoaded("uploadSecondaryImage")}
                      acceptedFiles={acceptedFileTypes}
                      maxSizeInMB={20}
                      maxFiles={1}
                      setMessage={setMessage}
                      dimensions={{ width: 121, height: 135 }}
                      sx={{ minWidth: "105px" }}
                    />
                  </>
                )}
              </div>
              <FormHelperText
                error={!!hookForm?.errors?.secondaryImage}
                sx={{ fontFamily: "Poppins", ml: 0 }}
              >
                {!secondaryImageLoaded &&
                  hookForm?.errors?.secondaryImage?.message}
              </FormHelperText>
            </div>
          </div>
          <div className="flex w-full flex-col gap-y-[6px]">
            <TextMui text="Descripción del producto*" type="inputLabel" />
            <div className="flex w-full flex-col p-3 border rounded-lg border-[#EBEBEB]">
              <Controller
                name="generalCoverage"
                control={hookForm?.control}
                render={({ field: { value, onChange } }) => {
                  return (
                    <TextEditor
                      onChange={onChange}
                      value={value}
                      quillRef={quillRef}
                      descriptionProduct={descriptionProduct}
                    />
                  );
                }}
              />
              <FormHelperText
                error={!!hookForm?.errors?.generalCoverage}
                sx={{ fontFamily: "Poppins", ml: 0 }}
              >
                {hookForm?.errors?.generalCoverage?.message}
              </FormHelperText>
            </div>
          </div>
          {productId && (
            <div className="flex w-full items-end justify-end">
              <ButtonMui
                fullWidth
                textbutton="Guardar cambios"
                startIcon={<IconifyIcon icon="ic:baseline-save" />}
                variantstyle="primary"
                minheight="46px"
                onClick={handleClickSave}
                type="submit"
                sx={{ maxWidth: "212px" }}
              />
            </div>
          )}
        </div>
      </div>
      {/* Cuadro de confirmación */}
      {/* {confirmDelete.show && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 font-[Poppins]">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <p>¿Estás seguro de que deseas borrar esta imagen?</p>
            <div className="flex justify-end gap-4 mt-4">
              <ButtonMui
                textbutton="Cancelar"
                variantstyle="secondary"
                onClick={() => setConfirmDelete({ key: "", show: false })}
              />
              <ButtonMui
                textbutton="Borrar"
                variantstyle="primary"
                onClick={() => {
                  handleConfirmDelete(confirmDelete.key);
                  setConfirmDelete({ key: "", show: false });
                }}
              />
            </div>
          </div>
        </div>
      )} */}

      <ConfirmationDialog
        show={confirmDelete.show}
        message="¿Estás seguro de que deseas borrar esta imagen?"
        onConfirm={() => handleConfirmDelete(confirmDelete.key)}
        onCancel={() => setConfirmDelete({ key: "", show: false })}
      />
    </div>
  );
};

export default InfoProductForm;
