"use client";
import React, { useMemo } from "react";
import { usePathname } from "next/navigation";
import navigation from "./navigation/vertical";
import VerticalNavLink from "./navigation/VerticalNavLink";
import SideBarLogo from "../../../assets/svg/side-bar-logo.svg";
import Image from "next/image";
import { Badge, Box, List, styled } from "@mui/material";
import TextMui from "src/app/components/ui/Text";
import Icon from "src/app/components/ui/icon";
import { useAuth } from "src/hooks/login/useAuth";
import { dummyEndorsements } from "../../endorsement-manager/data/dummy-endorsements";

const SideBar = () => {
  // ** AuthContext
  const { onLogout } = useAuth();
  const pathname = usePathname();

  // Determinar el elemento seleccionado basado en la ruta actual
  const currentSelectedOption = useMemo(() => {
    const currentItem = navigation.find(item => item.path === pathname);
    return currentItem ? currentItem.title : "Dashboard";
  }, [pathname]);

  // Función dummy para mantener compatibilidad con VerticalNavLink
  const setSelectedOption = () => {};

	// cuenta solo los que estén "En revisión"
  const pendingCount = useMemo(
    () =>
      dummyEndorsements.filter((e) => e.estatus === "En revisión").length,
    []
  );

	// ** Agregar el badge a la navegación
	const navigationCustom = useMemo(() => {
		return navigation.map((item) => {
			if (item.title === "Gestor de Endosos") {
				return {
					...item,
					badge: {
						count: pendingCount,
						show: pendingCount > 0,
					},
				};
			}
			return item;
		});
	}, [pendingCount]);

  // ** Styled Components

  const AvatarLetter = styled(Box)(({ theme }) => ({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",

    backgroundColor: "#10265F",
    color: theme.palette.primary.contrastText,
    borderRadius: "50%",

    width: "40px",
    height: "40px",
  }));

	return (
		<div className="flex w-[30%] rounded-[10px] bg-[#c3e8f5] h-[100%] flex-col items-center justify-between pt-[14px] pb-[10px] box-border gap-[9px] min-w-[268px] max-w-[268px]">
			<div className="flex flex-col w-full items-center gap-y-[30px] border-b border-b-[#7D7D7D] h-[90%]">
				<Image
					src={SideBarLogo}
					width={118}
					height={57}
					loading="lazy"
					alt="Logo"
				/>
				<List
					className=" flex flex-col gap-y-[20px]"
					sx={{ pt: 0, "& > :first-of-type": { mt: "0" } }}
				>
					<TextMui text="Páginas" type="subtitle" className="pl-[20px]" />
					{navigationCustom.map((item, index) => (
						<VerticalNavLink
							key={index}
							item={item}
							selectedOption={currentSelectedOption}
							setSelectedOption={setSelectedOption}
							classItem={
								currentSelectedOption === item.title ? "selected" : "not-selected"
							}
						/>
					))}
				</List>
			</div>
			<div className="flex w-full flex-row items-center justify-between px-[20px] cursor-pointer">
				<div className="flex felx-row items-center gap-x-[10px] ">
					<Badge
						overlap="circular"
						anchorOrigin={{
							vertical: "bottom",
							horizontal: "right",
						}}
					>
						<AvatarLetter>{`C`}</AvatarLetter>
					</Badge>
					<TextMui text="<EMAIL>" type="subtitle" />
				</div>
				<Icon
					icon={"tabler:logout"}
					color="#7D7D7D"
					fontSize={"18px"}
					onClick={onLogout}
				/>
			</div>
		</div>
	);
};

export default SideBar;
