import React, { HTMLAttributes } from "react";
import { cn } from "src/app/lib/utils";

interface TextProps extends HTMLAttributes<HTMLParagraphElement> {
	text: string | undefined;
	type?: "title" | "subtitle" | "medium" | "columnTable" | "inputLabel";
	className?: string;
}
const TextMui = ({ text, type, className, ...props }: TextProps) => {
	return (
		<p
			{...props}
			className={cn(
				`${type === "title" ? "text-[34px] text-[#7D7D7D]" : undefined}`,
				`${type === "medium" ? "text-2xl text-[#7D7D7D]" : undefined}`, // 24px
				`${type === "subtitle" ? "text-sm text-[#7D7D7D]" : undefined}`, // 14px
				`${
					type === "columnTable"
						? "text-base text-[#7D7D7D] font-bold"
						: undefined
				}`,
				`${type === "inputLabel" ? "text-[22px] text-[#6D6D6D]" : undefined}`,
				className,
				"font-[Poppins]"
			)}
		>
			{text}
		</p>
	);
};

export default TextMui;
