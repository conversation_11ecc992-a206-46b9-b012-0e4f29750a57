// DTOs para el endpoint /v1/endoso/search

export interface SearchEndorsementsDto {
  username?: string;
  endorsementId?: string;
  status?: string;
  order?: string;
  page?: number;
  limit?: number;
}

export interface EndorsementFile {
  endorsementFileId: number;
  endorsementId: number;
  fileId: number;
  file: {
    fileId: number;
    url: string;
    label: string;
    size: number;
    userId: number;
  };
}

export interface EndorsementSearchResult {
  endorsementId: number;
  tipo: string;
  fecha: string;
  estatus: string;
  policyId: number;
  userId: number;
  endorsementFiles?: EndorsementFile[];
}

export interface SearchEndorsementsResponseDto {
  data: EndorsementSearchResult[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
