// DTOs para el endpoint /v1/endoso/search

export interface SearchEndorsementsDto {
  username?: string;
  endorsementId?: string;
  status?: string;
  order?: string;
  page?: number;
  limit?: number;
}

export interface EndorsementSearchResult {
  endorsementId: string;
  title: string;
  description: string;
  status: string;
  endorsementType: string;
  files?: {
    endorsementFile: string;
    title: string;
    url: string;
    userId: number;
  }[];
}

export interface SearchEndorsementsResponseDto {
  message: string;
  error: boolean;
  statusCode: number;
  data: EndorsementSearchResult[];
}
