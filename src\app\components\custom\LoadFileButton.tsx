import { Box, SxProps, Theme } from "@mui/material";
import React from "react";
import Icon from "../ui/icon";
import ButtonMui from "../ui/Button";

interface LoadFileButtonProps {
	title: string;
	isDisabled?: boolean;
	maxSizeInMB: number;
	acceptedFiles: string[];
	inputRef: React.RefObject<HTMLInputElement | null>;
	onFileLoaded: (files: File | File[]) => void;
	maxFiles?: number;
	sx?: SxProps<Theme>;
	icon?: boolean;
	dropzone?: boolean;
	classname?: string;
	setMessage: React.Dispatch<React.SetStateAction<boolean>>;
	dimensions?: { width: number; height: number };
}

const LoadFileButton = ({
	title,
	isDisabled,
	maxSizeInMB,
	acceptedFiles,
	inputRef,
	onFileLoaded,
	maxFiles = 1,
	sx,
	icon,
	dropzone,
	classname,
	setMessage,
	dimensions,
}: LoadFileButtonProps) => {
	const onClick = () => {
		inputRef.current?.click();
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const files = event.target.files ? Array.from(event.target.files) : [];
		if (files.length > maxFiles) {
			alert(`Solo se puede agregar un máximo de ${maxFiles} archivo(s).`);

			return;

			// if (file.size <= maxSizeInMB * 1024 * 1024) {
			//   onFileLoaded(file)
			// } else {
			//   alert(`El archivo no debe exceder los ${maxSizeInMB} MB.`)
			// }
		}
		if (files) {
			const img = new Image();
			const reader = new FileReader();

			reader.onload = (e) => {
				if (e?.target?.result) {
					img.src = e.target.result as string;
				}
			};

			img.onload = () => {
				if (
					img.width === dimensions?.width &&
					img.height === dimensions?.height
				) {
					setMessage(true);
				} else {
					setMessage(false);
					event.target.value = ""; // Limpiar el input
				}
			};

			files.forEach((file) => reader.readAsDataURL(file));
		}

		const validFiles = files.filter(
			(file) => file.size <= maxSizeInMB * 1024 * 1024
		);

		if (validFiles.length < files.length) {
			alert(`Algunos archivos exceden el tamaño máximo de ${maxSizeInMB} MB.`);
		}

		if (validFiles.length > 0) {
			if (maxFiles === 1) {
				onFileLoaded(validFiles[0]);
			} else {
				onFileLoaded(validFiles);
			}
		}

		event.target.value = "";
	};

	const acceptedFileTypesString = acceptedFiles.join(",");

	return (
		<Box sx={sx}>
			<input
				type="file"
				ref={inputRef}
				onChange={handleFileChange}
				style={{ display: "none" }}
				accept={acceptedFileTypesString}
				multiple={maxFiles > 1}
			/>
			{icon ? (
				<Icon icon={"ic:outline-attach-file"} fontSize={20} onClick={onClick} />
			) : dropzone ? (
				<div className={classname} onClick={onClick} />
			) : (
				<ButtonMui
					textbutton={title}
					disabled={isDisabled}
					onClick={onClick}
					variantstyle="primary"
					startIcon={<Icon icon={"material-symbols:publish-rounded"} />}
					fullWidth
				/>
			)}
		</Box>
	);
};

export default LoadFileButton;
