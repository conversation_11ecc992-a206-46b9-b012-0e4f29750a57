import { useState } from "react";
import miscManagerService from "src/services/misc-manager/misc-manager.service";
import {
  GetBannersResponse,
  CreateBannerPayload,
} from "src/services/misc-manager/dtos/banners.dto";

export const useUpdateBanner = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateBanner = async (
    id: number,
    payload: Partial<CreateBannerPayload>
  ): Promise<GetBannersResponse | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await miscManagerService.updateBanner(id, payload);
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || "Error al actualizar el banner");
      } else {
        setError("Error al actualizar el banner");
      }
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { updateBanner, loading, error };
};
