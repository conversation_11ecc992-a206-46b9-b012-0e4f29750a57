"use client";

import React from "react";
import NoPromotionsView from "./views/NoPromotions";
import NoProductsView from "./views/NoProducts";
import PromotionsTableView from "./views/PromotionsTable";
import { useGetProductsPagination } from "src/hooks/productManager/useGetProductsPagination";
import { useGetPromotionsPagination } from "src/hooks/promotionManager/useGetPromotionPagination";
import { CircularProgress } from "@mui/material";

const PromotionManagerPage = () => {
  const { products } = useGetProductsPagination();
  const { promotions, loading } = useGetPromotionsPagination();

  if (loading) {
    return (
      <div className="flex items-center justifiy-center h-full w-[77%] flex-col max-w-[1146px]">
        <CircularProgress size={"4rem"} />
      </div>
    );
  }

  return (
    <>
      {products.length <= 0 ? (
        <NoProductsView />
      ) : promotions.length > 0 ? (
        <PromotionsTableView />
      ) : (
        <NoPromotionsView />
      )}
    </>
  );
};

export default PromotionManagerPage;
