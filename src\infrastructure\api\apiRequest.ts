import axiosInstance from "./axios.config";
import axios from "axios";

type Method = "GET" | "POST" | "PUT" | "DELETE";

type ApiResponse<T> = {
  data?: T;
  error?: string;
};

type ApiRequestArgs = {
  method: Method;
  url: string;
  data?: unknown;
  params?: unknown;
  headers?: Record<string, string>;
};

const apiRequest = async <T>({
  method,
  url,
  data,
  headers,
  params,
}: ApiRequestArgs): Promise<ApiResponse<T>> => {
  try {
    const isFormData = data instanceof FormData;

    const response = await axiosInstance({
      method,
      url,
      data,
      params,
      headers: {
        "Content-Type": isFormData ? "multipart/form-data" : "application/json",
        ...headers,
      },
    });
    return { data: response.data };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return { error: error.response?.data?.message || "An error occurred" };
    } else {
      return { error: "An unexpected error occurred" };
    }
  }
};

export default apiRequest;
