import { useEffect, useState } from 'react';
import { IBanner } from 'src/services/misc-manager/dtos/banners.dto';
import miscManagerService from 'src/services/misc-manager/misc-manager.service';

export const useGetBanners = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);
  const [banners, setBanners] = useState<IBanner[]>([]);

  const getBanners = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await miscManagerService.getBanners();
      setBanners(response.data);
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Error al obtener los banners');
        throw err;
      } else {
        setError('Error al obtener los banners');
        throw new Error('Error al obtener los banners');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBanners();
  }, []);

  return {
    banners,
    loading,
    error,
    refetch: getBanners, // <-- expone la función de recarga
  };
};
