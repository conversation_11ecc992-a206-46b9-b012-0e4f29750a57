"use client";
import ProgressStepper from "../components/ProgressStepper";
import DocumentsTable from "../components/DocumentsTable";
import { useRouter } from "next/navigation";
import Icon from "src/app/components/ui/icon";
import { useMemo } from "react";

interface Props {
  claimId: string;
}

const mockClaims = [
  { id: 1, status: "Documentos" },
  { id: 2, status: "Propuesta finiquito" },
  { id: 3, status: "Proceso de pago" },
  { id: 4, status: "Pagado" },
  { id: 5, status: "Documentos" },
  { id: 6, status: "Proceso de pago" },
  { id: 7, status: "Propuesta finiquito" },
  { id: 8, status: "Documentos" },
  { id: 9, status: "Propuesta finiquito" },
  { id: 10, status: "Propuesta finiquito" },
  { id: 11, status: "Propuesta finiquito" },
  { id: 12, status: "Propuesta finiquito" },
];

const getStepFromStatus = (status: string): number => {
  switch (status) {
    case "Documentos":
      return 0;
    case "Propuesta finiquito":
      return 1;
    case "Proceso de pago":
      return 2;
    case "Pagado":
      return 3;
    default:
      return 0;
  }
};

export default function DocumentReviewView({ claimId }: Props) {
  const router = useRouter();

  const currentClaim = useMemo(() => {
    return mockClaims.find((claim) => claim.id === parseInt(claimId));
  }, [claimId]);

  const activeStep = useMemo(() => {
    return currentClaim ? getStepFromStatus(currentClaim.status) : 0;
  }, [currentClaim]);
  const handleBack = (e: React.MouseEvent) => {
    e.preventDefault();

    router.push("/claims-manager");
  };
  return (
    <div className="flex flex-col items-center justify-center w-full px-6">
      <div className="w-full flex justify-center mt-6">
        <button
          type="button"
          onClick={handleBack}
          style={{ background: "none", border: "none", padding: 0 }}
        >
          <Icon
            icon="icon-park-outline:return"
            width="30"
            height="30"
            color="#7D7D7D"
            className="pr-5"
          />
        </button>
        <ProgressStepper activeStep={activeStep} />
      </div>
      <div className="w-full mt-6 h-screen">
        {activeStep === 0 && <DocumentsTable />}
        {activeStep === 1 && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Propuesta de Finiquito
            </h2>
            <p className="text-gray-600">
              Componente en desarrollo para la propuesta de finiquito.
            </p>
          </div>
        )}
        {activeStep === 2 && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Proceso de Pago
            </h2>
            <p className="text-gray-600">
              Componente en desarrollo para el proceso de pago.
            </p>
          </div>
        )}
        {activeStep === 3 && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Reclamación Finalizada
            </h2>
            <p className="text-gray-600">
              La reclamación ha sido procesada y pagada exitosamente.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
