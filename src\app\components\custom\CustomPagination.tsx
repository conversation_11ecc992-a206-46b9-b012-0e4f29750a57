// ** MUI Imports
import { Box } from "@mui/material";
import Pagination from "@mui/material/Pagination";

import { useTheme } from "@mui/material/styles";
import TextMui from "../ui/Text";

interface CustomPaginationProps {
	onPageChange: (
		_event: React.ChangeEvent<unknown> | null,
		page: number
	) => void;
	pagination: {
		currentPage: number;
		pageSize: number;
		totalPages: number;
		totalRecords: number;
	};
}

const CustomPagination = ({
	onPageChange,
	pagination,
}: CustomPaginationProps) => {
	const theme = useTheme();
	const page = parseInt(pagination.currentPage.toString());
	const pageCount = parseInt(pagination.totalPages.toString());
	const pageSize = parseInt(pagination.pageSize.toString());
	const rowCount = parseInt(pagination.totalRecords.toString());

	// page: numero de pagina actual
	// pageCount: cantidad de paginas totales
	// pageSize: cantidad de items por pagina
	// rowCount: cantidad de items totales

	return (
		<Box
			sx={{
				display: "flex",
				justifyContent: "space-between",
				alignItems: "center",
				backgroundColor: "#white",
				borderRadius: "0px 0px 20p.5x 20.5px",
				width: "100%",
				height: "auto",
				minHeight: "58px",
				[theme.breakpoints.down("md")]: {
					flexDirection: "column",
					gap: "8px",
					padding: "10px 0px",
				},
			}}
		>
			<Pagination
				color={"standard"}
				count={pageCount}
				page={page}
				onChange={(event, value) => onPageChange(event, value)}
			/>
			<TextMui
				text={`${1 + pageSize * (page - 1)}-
				${page * pageSize > rowCount ? rowCount : page * pageSize} of ${rowCount}`}
				className="pr-4"
			/>
		</Box>
	);
};

export default CustomPagination;
