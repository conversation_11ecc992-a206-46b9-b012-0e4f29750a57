import { AppAlpexApiGateWay } from "../app.equinox.api-getway";
import { MISC_MANAGER_ROUTES } from "src/configs/api-routes";
import { CreateBannerPayload, GetBannersResponse } from "./dtos/banners.dto";

class MiscManagerService {
  public async getBanners(): Promise<GetBannersResponse> {
    try {
      const { data: response } = await AppAlpexApiGateWay.get(
        `${MISC_MANAGER_ROUTES.BANNERS}/ASC`
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async createBanner(
    payload: CreateBannerPayload
  ): Promise<GetBannersResponse> {
    try {
      const { data: response } = await AppAlpexApiGateWay.post(
        `${MISC_MANAGER_ROUTES.BANNERS}`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async deleteBanner(id: number): Promise<void> {
    try {
      await AppAlpexApiGateWay.delete(`${MISC_MANAGER_ROUTES.BANNERS}/${id}`);
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }

  public async updateBanner(
    id: number,
    payload: Partial<CreateBannerPayload>
  ): Promise<GetBannersResponse> {
    try {
      const { data: response } = await AppAlpexApiGateWay.put(
        `${MISC_MANAGER_ROUTES.BANNERS}`,
        { ...payload, id },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      return response;
    } catch (error) {
      const errMessage = String(error);
      throw new Error(errMessage);
    }
  }
}

const miscManagerService = new MiscManagerService();
export default miscManagerService;
