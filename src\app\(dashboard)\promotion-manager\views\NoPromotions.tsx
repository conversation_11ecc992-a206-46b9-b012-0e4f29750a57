"use client";
import Image from "next/image";
import React from "react";
import TextMui from "src/app/components/ui/Text";

import AsaltoImg from "../../../assets/svg/product-manager/Asalto.svg";
import HakeoImg from "../../../assets/svg/product-manager/Hakeo.svg";
import WidgetImg from "../../../assets/svg/product-manager/Widget.svg";
import RodadaImg from "../../../assets/svg/product-manager/Rodada.svg";
import { Grid2 } from "@mui/material";
import Icon from "src/app/components/ui/icon";
import ButtonMui from "src/app/components/ui/Button";
import { useRouter } from "next/navigation";

const NoPromotionsView = () => {
  const router = useRouter();
  return (
    <div className="flex-1 h-auto rounded-[20.5px] bg-[#fff] max-w-[1146px] ">
      <Grid2
        container
        spacing={2}
        className="flex-row justify-between items-center px-[40px] py-[30px]"
      >
        <Grid2
          size={{ xs: 12, md: 6 }}
          sx={{
            display: "flex",
            justifyContent: {
              xs: "center",
              md: "flex-start",
            },
          }}
        >
          <Grid2 container spacing={2} maxWidth={"349px"}>
            <Grid2 size={{ xs: 12, sm: 6 }} className="flex justify-center">
              <Image
                loading="lazy"
                width={169.5}
                height={220}
                src={AsaltoImg}
                alt="Asalto"
              />
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6 }} className="flex justify-center">
              <Image
                loading="lazy"
                width={169.5}
                height={220}
                src={HakeoImg}
                alt="Hakeo"
              />
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6 }} className="flex justify-center">
              <Image
                loading="lazy"
                width={169.5}
                height={220}
                src={WidgetImg}
                alt="Widget"
              />
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6 }} className="flex justify-center">
              <Image
                loading="lazy"
                width={169.5}
                height={220}
                src={RodadaImg}
                alt="Rodada"
              />
            </Grid2>
          </Grid2>
        </Grid2>
        <Grid2
          size={{ xs: 12, md: 6 }}
          className="flex flex-col w-auto px-[10px] gap-y-[10px] items-end"
        >
          <div className="flex flex-col gap-y-[10px] text-right">
            <TextMui text="Aún no tienes promociones creadas" type="medium" />
            <TextMui
              text="Crea tu primer promoción para aplicar a cualquiera de tus productos."
              type="subtitle"
            />
          </div>
          <ButtonMui
            variantstyle="primary"
            textbutton="Crea nueva promoción"
            startIcon={<Icon icon={"ic:baseline-plus"} />}
            sx={{
              width: "100%",
              maxWidth: {
                xs: "100%",
                md: "220px",
              },
            }}
            onClick={() => router.push("/promotion-manager/add-promotion")}
          />
        </Grid2>
      </Grid2>
    </div>
  );
};

export default NoPromotionsView;
