"use client";

import { NextPage } from "next";
import React from "react";

import ProductsTableView from "./views/ProductsTable";
import NoProductsView from "./views/NoProducts";
import { useGetProductsPagination } from "src/hooks/productManager/useGetProductsPagination";
import { CircularProgress } from "@mui/material";

const ProductManagerPage: NextPage = () => {
	const { products, loading } = useGetProductsPagination();

	if (loading) {
		return (
			<div className="flex items-center justifiy-center h-full w-[77%] flex-col max-w-[1146px]">
				<CircularProgress size={"4rem"} />
			</div>
		);
	}

	return (
		<>{products.length > 0 ? <ProductsTableView /> : <NoProductsView />}</>
	);
};

export default ProductManagerPage;
