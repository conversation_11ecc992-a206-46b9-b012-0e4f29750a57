export interface PromotionFormValues {
  id?: number; // ID de la promoción
  name: string; // Nombre de la promoción
  description: string; // Descripción de la promoción
  typeDiscount: string; // Tipo de descuento (porcentaje, monto fijo, etc.)
  discount: number; // Valor del descuento
  startDate: Date; // Fecha de inicio de la promoción
  endDate: Date; // Fecha de finalización de la promoción
  status: string; // Estado de la promoción (Borrador, Aprobada, etc.)
  code: string; // Código de la promoción
  uses?: number; // Número de usos realizados
  maxUses: number; // Número máximo de usos permitidos
  products: ProductAssociatedPromotion[]; // Lista de IDs de productos asociados
}

export interface ProductAssociatedPromotion {
  productId: number; // ID del producto asociado
  insuredAmounts: number[]; // planes
}
