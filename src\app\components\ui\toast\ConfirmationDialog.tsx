import React from "react";
import ButtonMui from "src/app/components/ui/Button";

interface ConfirmationDialogProps {
  show: boolean; // Controla si el cuadro de confirmación se muestra
  message: string; // Mensaje a mostrar en el cuadro
  onConfirm: () => void; // Acción a ejecutar al confirmar
  onCancel: () => void; // Acción a ejecutar al cancelar
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  show,
  message,
  onConfirm,
  onCancel,
}) => {
  if (!show) return null; // No renderiza nada si `show` es falso

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 font-[Poppins]"
      style={{ zIndex: 1000 }}
    >
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <p>{message}</p>
        <div className="flex justify-end gap-4 mt-4">
          <ButtonMui
            textbutton="Cancelar"
            variantstyle="secondary"
            onClick={onCancel} // Llama a la acción de cancelar
          />
          <ButtonMui
            textbutton="Confirmar"
            variantstyle="primary"
            onClick={onConfirm} // Llama a la acción de confirmar
          />
        </div>
      </div>
    </div>
  );
};

export default ConfirmationDialog;
