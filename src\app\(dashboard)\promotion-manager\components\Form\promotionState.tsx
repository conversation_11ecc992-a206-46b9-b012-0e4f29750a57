"use client";
import { MenuItem } from "@mui/material";
import SelectMui from "src/app/components/ui/Select";
import TextMui from "src/app/components/ui/Text";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useFormContext } from "react-hook-form";
import { useState } from "react";
import { toast } from "sonner";
import ConfirmStatusChangeToast from "src/app/components/ui/toast/ConfirmStatusChangeToast";

const PromotionState = () => {
  const { setValue, watch } = useFormContext();
  const statusPromotion = ["Publicada", "Borrador", "Inactiva"];

  // Observa el estado actual de la promoción
  const promotionStatus = watch("status") || "Borrador";
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Reglas de transición de estados (opcional)
  const isValidTransition = (from: string, to: string): boolean => {
    if (from === "Inactiva" && to === "Publicada") return false;
    return true;
  };

  const handleConfirmStatusChange = (
    currentStatus: string,
    newStatus: string,
    t: string | number
  ) => {
    if (!isValidTransition(currentStatus, newStatus)) {
      setErrorMessage(
        `No se puede cambiar de ${currentStatus} a ${newStatus}.`
      );
      toast.dismiss(t);
      return;
    }
    setErrorMessage(null);
    setValue("status", newStatus);
    toast.dismiss(t);
  };

  const handlePromotionStatusChange = (newStatus: string) => {
    const currentStatus = promotionStatus;
    setErrorMessage(null);

    toast.custom(
      (t) => (
        <ConfirmStatusChangeToast
          currentStatus={currentStatus}
          newStatus={newStatus}
          onConfirm={() =>
            handleConfirmStatusChange(currentStatus, newStatus, t)
          }
          onCancel={() => toast.dismiss(t)}
          message="de la promoción"
        />
      ),
      { duration: Infinity }
    );
  };

  return (
    <div className="flex w-full bg-white rounded-lg flex-col px-[10px] py-3 gap-y-1">
      <TextMui
        text="Estado de la promoción"
        type="medium"
        className="text-[#333333]"
      />
      <SelectMui
        value={promotionStatus} // Valor actual del estado
        onChange={(e) => handlePromotionStatusChange(e.target.value as string)}
        fullWidth
        IconComponent={ExpandMoreIcon}
      >
        {statusPromotion.map((item, index) => (
          <MenuItem key={index} value={item} sx={{ fontFamily: "Poppins" }}>
            {item}
          </MenuItem>
        ))}
      </SelectMui>
      {errorMessage && (
        <p className="mt-1 text-sm text-red-500 font-[Poppins]">
          {errorMessage}
        </p>
      )}
    </div>
  );
};

export default PromotionState;
