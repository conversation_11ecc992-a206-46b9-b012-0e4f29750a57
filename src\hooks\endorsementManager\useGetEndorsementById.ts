import { useState, useEffect } from "react";
import { useSearchEndorsements } from "./useSearchEndorsements";
import { EndorsementSearchResult } from "src/services/endorsement-manager/dtos/search-endorsements.dto";

export const useGetEndorsementById = (endorsementId: string) => {
  const [endorsement, setEndorsement] = useState<EndorsementSearchResult | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const { searchEndorsements } = useSearchEndorsements();

  useEffect(() => {
    const fetchEndorsementById = async () => {
      if (!endorsementId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        // Buscar el endoso específico usando el endorsementId
        const response = await searchEndorsements({
          endorsementId: endorsementId,
          page: 1,
          limit: 1, // Solo necesitamos un resultado
        });

        // Verificar si encontramos el endoso
        if (response.data.length > 0) {
          setEndorsement(response.data[0]);
        } else {
          setError("Endoso no encontrado");
        }
      } catch (err) {
        console.error("Error al obtener el endoso:", err);
        setError("No se pudo cargar el endoso");
      } finally {
        setLoading(false);
      }
    };

    fetchEndorsementById();
  }, [endorsementId, searchEndorsements]);

  const refetch = async () => {
    if (!endorsementId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await searchEndorsements({
        endorsementId: endorsementId,
        page: 1,
        limit: 1,
      });

      if (response.data.length > 0) {
        setEndorsement(response.data[0]);
      } else {
        setError("Endoso no encontrado");
      }
    } catch (err) {
      console.error("Error al obtener el endoso:", err);
      setError("No se pudo cargar el endoso");
    } finally {
      setLoading(false);
    }
  };

  return {
    endorsement,
    loading,
    error,
    refetch,
  };
};
