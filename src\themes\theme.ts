"use client";
import { createTheme } from "@mui/material/styles";
import { poppins } from "src/app/fonts/fonts";

const theme = createTheme({
	components: {
		MuiButton: {
			styleOverrides: {
				root: {
					backgroundColor: "#10265F",
				},
			},
		},
		MuiMenu: {
			styleOverrides: {
				paper: {
					borderRadius: "10px",
					boxShadow:
						"rgb(76 78 100 / 20%) 0px 5px 5px -3px, rgb(76 78 100 / 14%) 0px 8px 10px 1px, rgb(76 78 100 / 12%) 0px 3px 14px 2px",
				},
			},
		},
		MuiFormHelperText: {
			styleOverrides: {
				root: {
					fontFamily: poppins.style.fontFamily,
				},
			},
		},
		MuiPagination: {
			styleOverrides: {
				root: {
					fontFamily: poppins.style.fontFamily,
				},
			},
		},
		MuiPaginationItem: {
			styleOverrides: {
				root: {
					fontFamily: poppins.style.fontFamily,
				},
			},
		},
	},
});

export default theme;
