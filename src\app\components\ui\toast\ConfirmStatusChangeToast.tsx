import React from "react";
import ButtonMui from "src/app/components/ui/Button";

interface ConfirmStatusChangeToastProps {
  currentStatus: string;
  newStatus: string;
  onConfirm: () => void;
  onCancel: () => void;
  message?: string;
}

const ConfirmStatusChangeToast: React.FC<ConfirmStatusChangeToastProps> = ({
  currentStatus,
  newStatus,
  onConfirm,
  onCancel,
  message,
}) => {
  return (
    <div className="p-4 rounded-xl bg-white border border-blue-900 shadow-md max-w-sm text-sm font-[Poppins]">
      <p className="text-black mb-4">
        ¿Está seguro de cambiar el estado {message} de
        <span className="font-semibold"> &quot;{currentStatus}&quot; </span>a
        <span className="font-semibold"> &quot;{newStatus}&quot;</span>?
      </p>
      <div className="flex justify-end gap-2">
        <ButtonMui
          textbutton="Confirmar"
          variantstyle="primary"
          onClick={onConfirm}
        />
        <ButtonMui
          textbutton="Cancelar"
          variantstyle="secondary"
          onClick={onCancel}
        />
      </div>
    </div>
  );
};

export default ConfirmStatusChangeToast;
