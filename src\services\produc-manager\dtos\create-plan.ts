export interface CreatePlanDto {
  productId: number;
  insuredAmount: string;
  netPremium: string;
  deductible: string;
  iva?: string;
  tieneIva: boolean;
  total: string;
}

export interface CreatePlanResponse {
  success: boolean;
  message: string;
  data?: {
    insuredAmount: string;
    netPremium: string;
    deductible: string;
    iva: string;
    tieneIva: boolean;
    total: string;
    productostId: {
      id: number;
    };
    id: number;
    deletedAt: string | null;
    productId: number;
  };
}
