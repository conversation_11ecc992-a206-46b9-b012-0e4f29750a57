import React from "react";

interface KpiCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  valueSuffix?: string; // Para "MXN", "%" etc.
}

const KpiCard = ({
  title,
  value,
  icon,
  description,
  valueSuffix,
}: KpiCardProps) => (
  <div
    className="
      w-full max-w-[271px] h-auto
      flex flex-col items-start justify-start
      rounded-[15.8px] bg-white
      shadow-[0_7.6px_15.1px_rgba(0,0,0,0.25)]
      p-4 box-border
    "
  >
    <div className="flex items-center gap-3 w-full">
      {icon && (
        <div className="text-4xl text-[#6d6d6d] flex-shrink-0 flex items-center">
          {icon}
        </div>
      )}
      <span className="text-lg font-medium text-[#6d6d6d]">{title}</span>
    </div>
    <div className="flex flex-col items-left justify-center flex-1 w-full">
      <span className="text-[40px] text-[#333]">
        {typeof value === "number" ? value.toLocaleString("es-MX") : value}
        {valueSuffix && <span className="ml-1">{valueSuffix}</span>}
      </span>
      {description && (
        <span className="text-[16px] text-[#6d6d6d] font-poppins mt-2 w-full">
          {description}
        </span>
      )}
    </div>
  </div>
);

export default KpiCard;
