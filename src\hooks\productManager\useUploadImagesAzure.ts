import productManagerService from "src/services/produc-manager/product-manager.service";

export const useUploadImagesAzure = () => {
	const uploadImagesAzure = async (params: FormData) => {
		try {
			const response = await productManagerService.uploadImagesAzure(params);
			return response;
		} catch (error) {
			console.error(error);
		}
	};
	
	const deleteImagesAzure = async (idImage: string) => {
		try {
			const response = await productManagerService.deleteImagesAzure(idImage);
			return response;
		} catch (error) {
			console.error(error);
		}
	};

	return { 
		uploadImagesAzure, 
		deleteImagesAzure,
	};
};
