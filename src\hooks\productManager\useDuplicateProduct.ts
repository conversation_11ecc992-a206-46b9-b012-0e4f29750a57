import productManagerService from "src/services/produc-manager/product-manager.service";

export const useDuplicateProduct = () => {
  const duplicateProduct = async (id: number) => {
    try {
      const response = await productManagerService.duplicateProduct(id);
      if (
        response &&
        Array.isArray(response.duplicatedProduct) &&
        response.duplicatedProduct.length > 0
      ) {
        return {
          success: true,
          message: response.message || "Producto duplicado correctamente",
        };
      }
      return {
        success: false,
        message: response.message || "No se pudo duplicar el producto",
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  };

  return { duplicateProduct };
};
