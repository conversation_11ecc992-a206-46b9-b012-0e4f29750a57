import { useEffect, useState } from "react";
import { Product } from "src/services/produc-manager/dtos/get-all-products-table-response.dto";
import productManagerService from "src/services/produc-manager/product-manager.service";

export const useGetProductsActive = () => {
  const [products, setProducts] = useState<Product[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProductsActive = async () => {
      try {
        setLoading(true);
        const response = await productManagerService.getProductsActive();
        setProducts(response);
      } catch (err) {
        console.error("Error al obtener los productos activos:", err);
        setError("No se pudieron cargar los productos activos.");
      } finally {
        setLoading(false);
      }
    };

    fetchProductsActive();
  }, []);

  return { products, loading, error, setProducts };
};
