"use client";

import React from "react";
import BannerForm from "./bannerForm/BannerForm";
import { useGetBanners } from "src/hooks/misc-manager/useGetBanners";
import { CircularProgress } from "@mui/material";

const MiscManagerPage = () => {
  const { banners, loading, refetch } = useGetBanners();

  if (loading) {
    return (
      <div className="flex items-center justifiy-center h-full w-[77%] flex-col max-w-[1146px]">
        <CircularProgress size={"4rem"} />
      </div>
    );
  }

  return (
    <section className="w-full mt-1">
      <h3
        className="pl-4 text-xl text-[#7D7D7D] font-medium"
        style={{ fontFamily: "Poppins" }}
      >
        Banners Home Page
      </h3>
      <div className="w-full max-w-5xl mx-auto p-8 shadow-sm mt-2 bg-white rounded-xl">
        <BannerForm banners={banners} refetchBanners={refetch} />
      </div>
    </section>
  );
};

export default MiscManagerPage;
