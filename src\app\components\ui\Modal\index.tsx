"use client";

import { Box, Dialog, Fade, styled } from "@mui/material";
import { ReactNode } from "react";

interface IAppModal {
	children: ReactNode;
	openModal: boolean;
	onClose: () => void;
	width?: string;
	gap?: string;
	padding?: string;
}

export default function ModalMui({
	children,
	openModal,
	onClose,
	width,
	gap,
	padding,
}: IAppModal) {
	return (
		<Dialog
			TransitionComponent={Fade}
			open={openModal}
			onClose={onClose}
			sx={{
				"& .MuiDialog-paper": {
					borderRadius: "8px",
					width: width || "616px",
					maxWidth: "calc(100% - 64px)",
				},
			}}
			slots={{ backdrop: StyledBackdrop }}
		>
			<Box
				sx={{
					display: "flex",
					flexDirection: "column",
					gap: gap || "24px",
					padding: padding || "24px",
				}}
			>
				{children}
			</Box>
		</Dialog>
	);
}

const StyledBackdrop = styled("div")`
	z-index: -1;
	position: fixed;
	inset: 0;
	background-color: rgba(76, 78, 100, 0.5);
	-webkit-tap-highlight-color: transparent;
`;
