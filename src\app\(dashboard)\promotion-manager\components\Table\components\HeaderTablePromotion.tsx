import { GridSearchIcon } from "@mui/x-data-grid";
import React from "react";
import ButtonMui from "src/app/components/ui/Button";
import InputMui from "src/app/components/ui/Input";

interface HeaderPromotionTableProps {
  onSearchFilters: (key: string, value: string | string[]) => void;
}

const HeaderTablePromotion = ({
  onSearchFilters,
}: HeaderPromotionTableProps) => {
  const buttonSelectedStyle = {
    "&:focus, &:active": {
      backgroundColor: "#10265F",
      color: "#ffffff",
      fontWeight: "bold",
    },
  };

  return (
    <div className="flex flex-col lg:flex-row w-auto h-auto p-[20px] items-center justify-between gap-y-[10px] lg:gap-y-0">
      <div className="flex items-center gap-x-[8px]">
        <ButtonMui
          textbutton="Todas"
          variantstyle="secondary"
          sx={buttonSelectedStyle}
          onClick={() => onSearchFilters("status", "")}
        />
        <ButtonMui
          textbutton="Activas"
          variantstyle="secondary"
          sx={buttonSelectedStyle}
          onClick={() => onSearchFilters("status", ["Publicada"])}
        />
        <ButtonMui
          textbutton="Borrador"
          variantstyle="secondary"
          sx={buttonSelectedStyle}
          onClick={() => onSearchFilters("status", ["Borrador"])}
        />
        <ButtonMui
          textbutton="Inactivas"
          variantstyle="secondary"
          sx={buttonSelectedStyle}
          onClick={() => onSearchFilters("status", ["Inactiva"])}
        />
      </div>
      <div className="flex items-center gap-x-[10px]">
        <InputMui
          placeholder="Buscar"
          size="small"
          slotProps={{
            input: {
              endAdornment: <GridSearchIcon />,
              sx: {
                "& .MuiOutlinedInput-notchedOutline": {
                  borderRadius: "8px",
                },
              },
            },
          }}
          onChange={(e) => onSearchFilters("name", e.target.value)}
        />
      </div>
    </div>
  );
};

export default HeaderTablePromotion;
