import * as yup from "yup";

export const BannerFormYupSchema = yup.object().shape({
  banners: yup
    .array()
    .of(
      yup.object().shape({
        url_banner: yup
          .string()
          .required("Debes subir una imagen para el banner"),
        linkUrl: yup
          .string()
          .url("El enlace debe ser una URL válida")
          //.notRequired(),
          .required("El enlace es obligatorio"),
        order: yup
          .number()
          .integer("El orden debe ser un número entero")
          .min(1, "El orden mínimo es 1")
          .max(6, "El orden máximo es 6")
          .notRequired(),
      })
    )
    .min(1, "Debe haber al menos un banner")
    .max(6, "Máximo 6 banners"),
});

export type BannerFormYupValues = yup.InferType<typeof BannerFormYupSchema>;
