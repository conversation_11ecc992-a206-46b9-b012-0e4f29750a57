"use client";
import React, { useState } from "react";
import {
  DataGrid,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridColDef,
} from "@mui/x-data-grid";

import "../../../styles/global-table-ui-styles.css";
import TextMui from "src/app/components/ui/Text";
import Icon from "src/app/components/ui/icon";
import { EFieldPromotionTable } from "../components/Table/enum/promotion-table.enum";
import ColumnHeader from "../../product-manager/components/Table/components/ColumnHeader";
import CustomPagination from "src/app/components/custom/CustomPagination";
import { Menu, MenuItem } from "@mui/material";
import { useRouter } from "next/navigation";
import SelectPromotionState from "../components/Table/selectState";
import { useGetPromotionsPagination } from "src/hooks/promotionManager/useGetPromotionPagination";
import { Promotion } from "src/services/promotion-manager/dtos/get-all-promotions-table-response.dto";
import { formatDateToDMY } from "src/helper/formatDateToDMY";
import HeaderTablePromotion from "../components/Table/components/HeaderTablePromotion";
import { useDuplicatePromotion } from "src/hooks/promotionManager/useDuplicatePromotion";
import { useToast } from "src/hooks/useToast";

export interface PromotionsTableViewProps {
  promotions: Promotion[];
}

const PromotionsTableView = () => {
  // ** Next Hooks

  const router = useRouter();
  // ** Custom Hooks
  const {
    loading,
    promotionManagerPagination,
    setPromotionManagerPagination,
    pagination,
    promotions,
    //getPromotionsPagination,
    //setLoading,
  } = useGetPromotionsPagination();

  const { duplicatePromotion } = useDuplicatePromotion();
  const { toast } = useToast();

  // ** Local States

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  // const [modalShow, setModalShow] = useState<boolean>(false);

  const [selectedPromotion, setSelectedPromotion] = useState<number | null>(
    null
  );

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setSelectedPromotion(null);
    setAnchorEl(null);
  };

  const onSearchFilters = (key: string, value: string | string[]) => {
    setPromotionManagerPagination((prevState) => {
      const existingFilterIndex = prevState.filters.findIndex(
        (filter) => filter.type === key
      );
      const newFilters = [...prevState.filters];

      if (existingFilterIndex !== -1) {
        // Si el filtro existe
        if (value === "" || (Array.isArray(value) && value.length === 0)) {
          // Elimina el filtro si el valor es vacío
          newFilters.splice(existingFilterIndex, 1);
        } else {
          // Actualiza el filtro existente
          newFilters[existingFilterIndex] = { type: key, value };
        }
      } else {
        // Si el filtro no existe y el valor no es vacío, agrégalo
        if (value !== "" && (!Array.isArray(value) || value.length > 0)) {
          newFilters.push({ type: key, value });
        }
      }

      return {
        ...prevState,
        filters: newFilters,
        pagination: {
          ...prevState.pagination,
          currentPage: 1,
        },
      };
    });
  };

  const handleDuplicate = (id: number | null) => {
    if (!id) return;
    duplicatePromotion(id)
      .then((response) => {
        const duplicatePromotionId = response.data.id;
        router.push(
          `/promotion-manager/edit-promotion/${duplicatePromotionId}`
        );
      })
      .catch((error) => {
        toast.error("No se pudo duplicar la promoción");
        console.error("Error duplicating promotion:", error);
      });
    //console.log("Duplicate promotion with ID:", id);
    handleClose();
  };

  const handleEdit = (id: string) => {
    //console.log("Edit promotion with ID:", id);
    handleClose();
    router.push(`/promotion-manager/edit-promotion/${id}`);
  };

  const ModalActions = () => {
    return (
      <>
        <Menu
          disableScrollLock={true}
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          <MenuItem
            onClick={() => {
              if (selectedPromotion !== null) {
                handleEdit(selectedPromotion.toString());
              }
            }}
            sx={{
              minWidth: "172px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <TextMui text="Editar" type="subtitle" />
            <Icon icon="ic:outline-edit" fontSize={24} color="#7D7D7D" />
          </MenuItem>

          <MenuItem
            onClick={() => handleDuplicate(selectedPromotion)}
            sx={{
              minWidth: "172px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <TextMui text="Duplicar" type="subtitle" />
            <Icon
              icon="ic:outline-content-copy"
              fontSize={24}
              color="#7D7D7D"
            />
          </MenuItem>
        </Menu>
      </>
    );
  };

  const onPageChange = (
    _event: React.ChangeEvent<unknown> | null,
    value: number
  ) => {
    setPromotionManagerPagination({
      ...promotionManagerPagination,

      pagination: {
        ...promotionManagerPagination.pagination,
        currentPage: value,
      },
    });
  };

  const columns: GridColDef<Promotion>[] = [
    {
      ...GRID_CHECKBOX_SELECTION_COL_DEF,
    },
    {
      flex: 1,
      field: EFieldPromotionTable.PROMOTION,
      headerName: "Promoción",
      minWidth: 280,
      type: "string",
      align: "center",
      headerAlign: "center",
      display: "flex",
      disableColumnMenu: true,
      headerClassName: "column-header bg-[#fff]",
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui text={row.name} type="columnTable" className="font-normal" />
      ),
    },
    // {
    //   flex: 1,
    //   field: EFieldPromotionTable.ASSOCIATED_PRODUCT,
    //   headerName: "Producto asociado",
    //   minWidth: 205,
    //   type: "string",
    //   align: "center",
    //   headerClassName: "column-header bg-[#fff]",
    //   headerAlign: "center",
    //   disableColumnMenu: true,
    //   sortable: false,
    //   renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
    //   renderCell: ({ row }) => (
    //     <TextMui
    //       text={row.associatedProduct}
    //       type="columnTable"
    //       className="font-normal"
    //     />
    //   ),
    // },
    {
      flex: 2,
      field: EFieldPromotionTable.EFFECTIVE_DATE,
      headerName: "Fecha de vigencia",
      minWidth: 80,
      type: "string",
      align: "center",
      headerAlign: "center",
      headerClassName: "column-header bg-[#fff]",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui
          text={formatDateToDMY(row.endDate)}
          type="columnTable"
          className="font-normal"
        />
      ),
    },
    {
      flex: 1,
      field: EFieldPromotionTable.TOTAL_TIMES_USED,
      headerName: "Total de veces utilizada",
      minWidth: 180,
      type: "string",
      align: "center",
      headerAlign: "center",
      headerClassName: "column-header bg-[#fff]",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <TextMui
          text={String(row.uses)}
          type="columnTable"
          className="font-normal"
        />
      ),
    },
    {
      flex: 1,
      field: EFieldPromotionTable.STATE,
      headerName: "Estado",
      minWidth: 100,
      type: "string",
      align: "center",
      headerAlign: "center",
      headerClassName: "column-header bg-[#fff]",
      disableColumnMenu: true,
      sortable: false,
      renderHeader: ({ colDef }) => <ColumnHeader colDef={colDef} />,
      renderCell: ({ row }) => (
        <SelectPromotionState idPromotion={row.id} status={row.status} />
      ),
    },
    {
      flex: 1,
      field: EFieldPromotionTable.ACTIONS,
      headerName: "",
      type: "actions",
      minWidth: -10,
      sortable: false,
      disableColumnMenu: true,
      headerClassName: "column-header bg-[#fff]",
      getActions: ({ row }) => {
        return [
          <GridActionsCellItem
            key={row.id}
            icon={
              <Icon
                icon={"ic:baseline-more-vert"}
                color="#7D7D7D"
                style={{ fontSize: 16 }}
              />
            }
            label="actions"
            onClick={(e) => {
              setSelectedPromotion(row.id);

              handleClick(e);
            }}
          />,
        ];
      },
    },
  ];

  return (
    <div className="flex h-full w-full flex-col rounded-[20.5px] bg-[#fff] min-w-[500px] ">
      <HeaderTablePromotion onSearchFilters={onSearchFilters} />
      <DataGrid
        loading={loading}
        checkboxSelection
        rows={promotions}
        columns={columns}
        disableColumnFilter
        disableRowSelectionOnClick
        hideFooterSelectedRowCount
        getRowHeight={() => "auto"}
        getRowId={(row) => row.id}
        pagination
        slots={{
          pagination: () => (
            <CustomPagination
              onPageChange={onPageChange}
              pagination={pagination}
            />
          ),
        }}
        className="account-datagrid"
        sx={{
          border: "none",
          height: "auto",
        }}
      />
      <ModalActions />
    </div>
  );
};

export default PromotionsTableView;
