"use client";

import { useEffect, useState } from "react";
import { Product } from "src/services/produc-manager/dtos/get-all-products-table-response.dto";
import { GetAllProductsTableDto } from "src/services/produc-manager/dtos/get-all-products-table.dto";
import productManagerService from "src/services/produc-manager/product-manager.service";

const initialState: GetAllProductsTableDto = {
	filters: [],
	pagination: {
		currentPage: 1,
		pageSize: 10,
		totalPages: 0,
		totalRecords: 0,
	},
};

export const useGetProductsPagination = () => {
	const [productManagerPagination, setProductManagerPagination] =
		useState<GetAllProductsTableDto>(initialState);
	const [products, setProducts] = useState<Product[]>([]);
	const [pagination, setPagination] = useState(initialState.pagination);
	const [loading, setLoading] = useState<boolean>(true);

	const getProductsPagination = async (params: GetAllProductsTableDto) => {
		const { data, pagination } =
			await productManagerService.getProductsPagination(params);
		setProducts(data);
		setPagination(pagination);
		setLoading(false);

		return { products, pagination };
	};

	useEffect(() => {
		setLoading(true);
		if (productManagerPagination) {
			getProductsPagination(productManagerPagination);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [productManagerPagination]);

	return {
		products,
		pagination,
		loading,
		productManagerPagination,
		setProductManagerPagination,
		setLoading,
		setPagination,
		getProductsPagination,
	};
};
