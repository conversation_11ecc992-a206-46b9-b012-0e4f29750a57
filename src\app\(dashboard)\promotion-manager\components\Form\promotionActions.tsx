import ButtonMui from "src/app/components/ui/Button";
import IconifyIcon from "src/app/components/ui/icon";

interface PromotionActionsProps {
  onSubmit: () => void; // Recibe la función para manejar el envío
  isEdit?: boolean;
}

const PromotionActions = ({
  onSubmit,
  isEdit = false,
}: PromotionActionsProps) => {
  return (
    <ButtonMui
      fullWidth
      textbutton={isEdit ? "Guardar cambios" : "Registrar Promoción"}
      startIcon={<IconifyIcon icon="ic:baseline-save" />}
      variantstyle="primary"
      minheight="46px"
      type="submit"
      onClick={onSubmit}
    />
  );
};

export default PromotionActions;
