"use client";
import React, { useEffect, useRef, useState } from "react";
import { Controller } from "react-hook-form";
import {
  Tab,
  Tabs,
  IconButton,
  FormHelperText,
  Typography,
  Box,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ButtonMui from "src/app/components/ui/Button";
import Icon from "src/app/components/ui/icon";
import LoadFileButton from "src/app/components/custom/LoadFileButton";
import TextMui from "src/app/components/ui/Text";
import InputMui from "src/app/components/ui/Input";
import { useBannerForm } from "../hooks/useBannerForm";
import {
  IBanner,
  PayloadBannerSubmit,
} from "src/services/misc-manager/dtos/banners.dto";
import { useUploadImagesAzure } from "src/hooks/productManager/useUploadImagesAzure";
import { useCreateBanner } from "src/hooks/misc-manager/useCreateBanner";
import ConfirmationDialog from "src/app/components/ui/toast/ConfirmationDialog";
import { useDeleteBanner } from "src/hooks/misc-manager/useDeleteBanner";
import { useUpdateBanner } from "src/hooks/misc-manager/useUpdateBanner";

const TabsStyles = {
  maxWidth: "100%",
  "& .MuiTab-root": {
    color: "#10265F",
    textTransform: "capitalize",
    border: "1px solid #10265F",
    fontFamily: "Poppins",
    fontSize: "14px",
    fontWeight: 400,
    borderRight: "none",
  },
  "& .MuiTab-root:first-of-type": {
    borderTopLeftRadius: "8px",
    borderBottomLeftRadius: "8px",
    borderRight: "none",
  },
  "& .MuiTab-root:last-of-type": {
    borderTopRightRadius: "8px",
    borderBottomRightRadius: "8px",
    borderRight: "1px solid #10265F",
  },
  "& .MuiButtonBase-root.MuiTab-root.Mui-selected": {
    color: "#ffffff",
    backgroundColor: "#10265F",
    textTransform: "capitalize",
  },
};

type BannerFormProps = {
  banners: IBanner[];
  refetchBanners?: () => Promise<unknown>; // Permite cualquier tipo de promesa
};

const BannerForm = ({ banners, refetchBanners }: BannerFormProps) => {
  const { uploadImagesAzure, deleteImagesAzure } = useUploadImagesAzure();
  const { createBanner } = useCreateBanner();
  const { deleteBanner } = useDeleteBanner();
  const { updateBanner } = useUpdateBanner();

  const [showSuccess, setShowSuccess] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<{
    show: boolean;
    index: number | null;
    id?: number;
  }>({ show: false, index: null, id: undefined });
  const [originalBanners, setOriginalBanners] = useState<IBanner[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const handleBannerImageUpload = async (
    file: File,
    onChange: (url: string) => void
  ) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("name", file.name);

    const response = await uploadImagesAzure(formData);
    const uploadedUrl = response?.url || "";
    if (uploadedUrl) {
      onChange(uploadedUrl);
    }
  };

  const handleDeleteBannerImage = async (index: number) => {
    // Obtén la URL actual del banner
    const url = control._formValues?.banners?.[index]?.url_banner;
    if (url) {
      // Extrae el identificador de la imagen de la URL
      const urlParts = url.split("/");
      const fileName = urlParts[urlParts.length - 1].split("?")[0];

      try {
        await deleteImagesAzure(fileName);
      } catch (error) {
        console.error("Error eliminando imagen:", error);
      }
    }
    // Limpia el campo en el formulario y el archivo local
    setValue(`banners.${index}.url_banner`, "");
    setFilesToUpload((prev) => {
      const updated = [...prev];
      updated[index] = undefined;
      return updated;
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function getChangedFields(original: any, updated: any) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const changed: Record<string, any> = {};
    Object.keys(updated).forEach((key) => {
      if (key === "backendId" || key === "id") return;
      if (updated[key] !== original[key]) {
        changed[key] = updated[key];
      }
    });
    return changed;
  }

  const onSubmit = async (data: PayloadBannerSubmit) => {
    try {
      setIsSaving(true);
      const banners = data.banners ?? [];
      // Ordenar los banners por su order antes de guardar
      const sortedBanners = [...banners].sort((a, b) => {
        const orderA = typeof a.order === 'number' ? a.order : 0;
        const orderB = typeof b.order === 'number' ? b.order : 0;
        return orderA - orderB;
      });
      for (const banner of sortedBanners) {
        const payload = {
          url_banner: banner.url_banner,
          linkUrl: banner.linkUrl,
          order: typeof banner.order === 'number' ? banner.order : 1, // Asegura que siempre sea un número
        };

        if ("id" in banner && banner.id) {
          const original = originalBanners.find((b) => b.id === banner.id);
          const changes = getChangedFields(original, banner);
          if (Object.keys(changes).length > 0) {
            await updateBanner(banner.id, changes);
          }
        } else {
          await createBanner(payload);
        }
      }
      setShowSuccess(true); // Mostrar mensaje de éxito
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Espera 3 segundos
      setShowSuccess(false);
      if (refetchBanners) await refetchBanners(); // Recarga los banners después de crear
    } catch (error) {
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };
  const { fields, append, remove, control, formState, handleSubmit, setValue } =
    useBannerForm(onSubmit);

  useEffect(() => {
    if (banners && banners.length > 0) {
      setOriginalBanners(banners);
      setValue(
        "banners",
        banners.map((b) => ({
          ...b,
          backendId: b.id,
          id: b.id ?? undefined,
          linkUrl: b.linkUrl ?? "",
        }))
      );
    }
  }, [banners, setValue]);

  const [tabIndex, setTabIndex] = useState(0);
  const uploadFilesRefs = useRef<React.RefObject<HTMLInputElement>[]>([]);
  if (uploadFilesRefs.current.length !== fields.length) {
    // Asegura que haya un ref por cada banner
    uploadFilesRefs.current = Array(fields.length)
      .fill(null)
      .map(
        (_, i) =>
          uploadFilesRefs.current[i] || React.createRef<HTMLInputElement>()
      );
  }

  const [filesToUpload, setFilesToUpload] = useState<(File | undefined)[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [message, setMessage] = useState<boolean>(false);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const handleFileLoaded = (index: number) => (files: File | File[]) => {
    const file = Array.isArray(files) ? files[0] : files;
    setFilesToUpload((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
  };

  const handleRemoveBanner = (index: number) => {
    remove(index);
    setFilesToUpload((prev) => {
      const updated = [...prev];
      updated.splice(index, 1); // Elimina el archivo del banner eliminado
      return updated;
    });
    setTabIndex((prevValue) => {
      const newLength = fields.length - 1; // Longitud después de eliminar
      return prevValue >= newLength ? Math.max(newLength - 1, 0) : prevValue;
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex flex-col w-full" style={{ fontFamily: "Poppins" }}>
        <div className="flex w-full flex-col gap-y-[4px]">
          <div className="flex w-full flex-col gap-y-[12px]">
            <div className="w-full flex">
              <div className="flex w-full overflow-x-auto">
                <Tabs
                  value={tabIndex}
                  onChange={handleChange}
                  aria-label="banner tabs"
                  variant="scrollable"
                  textColor="inherit"
                  scrollButtons
                  allowScrollButtonsMobile
                  TabIndicatorProps={{
                    style: {
                      display: "none",
                      color: "#10265F",
                    },
                  }}
                  sx={{
                    ...TabsStyles,
                    minHeight: "55px", // Ajusta la altura mínima del contenedor de tabs
                    "& .MuiTab-root": {
                      minHeight: "55px", // Ajusta la altura mínima de cada tab
                      paddingTop: "4px",
                      paddingBottom: "4px",
                      ...TabsStyles["& .MuiTab-root"],
                    },
                  }}
                >
                  {fields?.map((item, index) => (
                    <Tab
                      key={item.id}
                      label={`Banner ${(typeof item.order === 'number' && !isNaN(item.order)) ? index = item.order  : index + 1} `}
                      icon={
                        fields.length > 1 ? (
                          <Icon
                            icon={"mynaui:trash"}
                            color="#fff"
                            onClick={(e) => {
                              e.stopPropagation();
                              setConfirmDelete({
                                show: true,
                                index: fields.findIndex(f => f.order === item.order),
                                id:
                                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                  typeof (item as any).backendId === "number"
                                    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                      (item as any).backendId
                                    : undefined,
                              });
                            }}
                            style={{ cursor: "pointer" }}
                          />
                        ) : undefined
                      }
                      iconPosition="end"
                    />
                  ))}
                </Tabs>
              </div>
              <div className="flex items-center gap-3 ml-4">
                <span className="text-sm text-[#A0A0A0] whitespace-nowrap">
                  Máximo 6 Banners
                </span>
                <ButtonMui
                  textbutton="Añadir"
                  variantstyle="add"
                  size="small"
                  fullWidth
                  startIcon={<Icon icon={"ic:baseline-plus"} />}
                  disabled={fields.length >= 6}
                  onClick={() => {
                    // Obtener los orders ya usados
                    const usedOrders = fields.map(f => typeof f.order === 'number' ? f.order : null).filter(o => o !== null);
                    // Buscar el primer número disponible a partir de 1
                    let newOrder = 1;
                    while (usedOrders.includes(newOrder)) {
                      newOrder++;
                    }
                    append?.({
                      url_banner: "",
                      linkUrl: "",
                      order: newOrder,
                    });
                    setTabIndex(fields?.length || 0);
                  }}
                  sx={{
                    px: 2,
                    height: "45px",
                  }}
                />
              </div>
            </div>
            {fields?.map((item, index) => {
              const urlBannerValue =
                control._formValues?.banners?.[index]?.url_banner ?? "";
              const uploadBannerIconLoaded =
                filesToUpload[index] ||
                (urlBannerValue ? { name: urlBannerValue } : undefined);
              const bannerErrors = formState?.errors?.banners;
              const linkUrlError = Array.isArray(bannerErrors)
                ? bannerErrors[index]?.linkUrl
                : undefined;
              return (
                <div key={item.id} hidden={tabIndex !== index}>
                  <div className="flex w-full flex-col gap-y-[12px]">
                    {/* Imagen */}
                    <div className="flex w-full flex-col">
                      <div className="flex w-full flex-col">
                        <div className="flex w-full min-h-[78px] border border-dashed items-center justify-between">
                          {uploadBannerIconLoaded ? (
                            <div className="flex w-full items-center justify-center">
                              <a
                                href={urlBannerValue}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                }}
                              >
                                <Icon
                                  icon={"carbon:image"}
                                  color="#10265F"
                                  className="text-4xl cursor-pointer"
                                />
                              </a>
                              <TextMui
                                text="Imagen cargada"
                                type="subtitle"
                                className=""
                              />
                              <IconButton
                                onClick={() => handleDeleteBannerImage(index)}
                              >
                                <Icon icon={"mynaui:trash"} color="#10265F" />
                              </IconButton>
                            </div>
                          ) : (
                            <>
                              <div className="flex w-full items-center justify-center">
                                <Icon icon={"carbon:image"} color="#AFAFAF" />
                                <TextMui
                                  text="Agregar Imagen (Recomendado 1920x750 px)"
                                  type="subtitle"
                                  className="pl-2"
                                />
                              </div>
                              <Controller
                                name={`banners.${index}.url_banner`}
                                control={control}
                                render={({ field }) => (
                                  <LoadFileButton
                                    {...field}
                                    setMessage={setMessage}
                                    title="Subir"
                                    inputRef={uploadFilesRefs.current[index]}
                                    onFileLoaded={async (files) => {
                                      const file = Array.isArray(files)
                                        ? files[0]
                                        : files;
                                      if (file) {
                                        await handleBannerImageUpload(
                                          file,
                                          field.onChange
                                        );
                                        handleFileLoaded(index)(files);
                                      }
                                    }}
                                    acceptedFiles={[
                                      "image/png",
                                      "image/jpeg",
                                      "image/jpg",
                                      "image/svg+xml",
                                    ]}
                                    maxSizeInMB={20}
                                    maxFiles={1}
                                    sx={{
                                      minWidth: "105px",
                                      marginRight: "10px",
                                    }}
                                  />
                                )}
                              />
                            </>
                          )}
                        </div>
                        <FormHelperText
                          error={
                            !!formState?.errors?.banners &&
                            Array.isArray(formState.errors.banners) &&
                            !!formState.errors.banners[index]?.url_banner
                          }
                          sx={{ fontFamily: "Poppins", ml: 0 }}
                        >
                          {formState?.errors?.banners &&
                          Array.isArray(formState.errors.banners)
                            ? formState.errors.banners[index]?.url_banner
                                ?.message
                            : ""}
                        </FormHelperText>
                      </div>
                    </div>
                    {/* Enlace */}
                    <div className="flex w-full flex-col gap-y-[6px]">
                      <TextMui text="Enlace" type="inputLabel" />
                      <Controller
                        name={`banners.${index}.linkUrl`}
                        control={control}
                        render={({ field: { value, onChange, onBlur } }) => (
                          <InputMui
                            value={value || ""}
                            onChange={onChange}
                            onBlur={onBlur}
                            placeholder="Añadir enlace"
                            customstyle="custom"
                            error={!!linkUrlError}
                            helperText={linkUrlError?.message}
                            slotProps={{
                              input: {
                                startAdornment: (
                                  <Icon
                                    icon="mdi:link-variant"
                                    color="#6d6d6d"
                                    className="mr-2 text-xl"
                                  />
                                ),
                                style: {
                                  height: "70px",
                                },
                              },
                              formHelperText: {
                                sx: {
                                  fontFamily: "Poppins",
                                  ml: 0,
                                },
                              },
                            }}
                          />
                        )}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
            {/* Botón guardar */}
            <div className="flex w-full items-center justify-end">
              {showSuccess && (
                <Box
                  display="flex"
                  alignItems="center"
                  gap={1}
                  bgcolor="#eefbe5"
                  color="#72E128"
                  px={2}
                  py={2}
                  borderRadius={2}
                  mr={2}
                >
                  <CheckCircleOutlineIcon fontSize="small" />
                  <Typography
                    variant="body2"
                    fontWeight={500}
                    fontFamily={"Poppins"}
                  >
                    Cambios guardados
                  </Typography>
                </Box>
              )}
              <ButtonMui
                fullWidth
                textbutton={isSaving ? "Guardando..." : "Guardar cambios"}
                startIcon={ <Icon icon="ic:baseline-save" />}
                loading={isSaving}
                variantstyle="primary"
                minheight="46px"
                type="submit"
                sx={{ maxWidth: "212px", borderRadius: "30px", color: '#fff' }}
                disabled={isSaving}
              />
            </div>
          </div>
        </div>
      </div>
      <ConfirmationDialog
        show={confirmDelete.show}
        message={`¿Estás seguro de que deseas borrar el banner ${
          confirmDelete.index !== null ? confirmDelete.index + 1 : ""
        }?`}
        onConfirm={async () => {
          if (confirmDelete.index !== null) {
            // Buscar el índice real del banner a eliminar según el order
            const idxToRemove = fields.findIndex(f => f.order === fields[confirmDelete.index!].order);
            if (confirmDelete.id) {
              await deleteBanner(confirmDelete.id);
            }
            handleRemoveBanner(idxToRemove);
          }
          setConfirmDelete({ show: false, index: null });
        }}
        onCancel={() => setConfirmDelete({ show: false, index: null })}
      />
    </form>
  );
};

export default BannerForm;
