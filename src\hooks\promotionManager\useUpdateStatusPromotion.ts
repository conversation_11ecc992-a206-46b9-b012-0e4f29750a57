import { useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";
import { UpdateStatusPromotionDto } from "src/services/promotion-manager/dtos/update-status-promotion.dto";

export function useUpdateStatusPromotion() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<unknown>(null);

  const updateStatusPromotion = async (payload: UpdateStatusPromotionDto) => {
    setLoading(true);
    setError(null);
    try {
      const response = await promotionManagerService.updateStatusPromotion(
        payload
      );
      setData(response);
      return response;
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Error al actualizar el estado de la promoción"
      );
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    updateStatusPromotion,
    loading,
    error,
    data,
  };
}
