export enum AUTH_ROUTES {
  LOGIN = "/api/v1/auth/login-admin",
}

export enum PRODUCT_MANAGER_ROUTES {
  GET_ALL_PRODUCTS_TABLE = "/api/v1/products/list",
  UPLOAD_IMAGES_AZURE = "/api/v1/azure",
  ADD_PRODUCT = "/api/v1/products",
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  GET_PRODUCT_BY_ID = "/api/v1/products",
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  UPDATE_PRODUCT = "/api/v1/products",
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  DELETE_PRODUCT = "/api/v1/products",
  CATEGORIES = "/api/v1/categories",
  DUPLICATE_PRODUCT = "/api/v1/products/duplicate",
  PLANS = "/api/v1/plans",
  GET_PRDUCTS_ACTIVE = "/api/v1/products/products/active",
}

export enum ENDORSEMENT_MANAGER_ROUTES {
  GET_ALL_ON_REVIEW = "/api/v1/endoso/all-on-review",
  SEARCH_ENDORSEMENTS = "/api/v1/endoso/search",
}

export enum PROMOTION_MANAGER_ROUTES {
  GET_ALL_PROMOTIONS_TABLE = "/api/v1/promotions/list",
  PROMOTIONS = "/api/v1/promotions",
}

export enum MISC_MANAGER_ROUTES {
  BANNERS = "/api/v1/banners",
}
