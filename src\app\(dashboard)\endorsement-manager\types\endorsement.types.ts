// Tipos para el manejo de endosos
export type EndorsementStatus = 
  | "En revisión" 
  | "Por actualizar" 
  | "Actualizado" 
  | "Cancelado";

export type EndorsementType =
  | "Cambio de domicilio"
  | "Cancelación"
  | "Cambio de nombre";


export interface EndorsementFile {
  endorsementFileId: number;
  endorsementId: number;
  fileId: number;
  file: {
    fileId: number;
    url: string;
    label: string;
    size: number;
    userId: number;
  };
}

export interface Endorsement {
  id: string;
  usuario: string;
  tipoEndoso: EndorsementType;
  fechaRegistro: string;
  estatus: EndorsementStatus;
  endorsementFiles?: EndorsementFile[];
}

export interface EndorsementFilters {
  status: EndorsementStatus | "Todos" | "" | null;
  searchTerm: string;
  order: "ASC" | "DESC";
}

export interface EndorsementTableProps {
  endorsements: Endorsement[];
  loading?: boolean;
  onSearchFilters: (key: string, value: string | string[] | null) => void;
  onPageChange?: (page: number) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
  };
}
