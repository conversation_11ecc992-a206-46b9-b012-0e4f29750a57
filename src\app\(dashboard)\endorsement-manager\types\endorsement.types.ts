// Tipos para el manejo de endosos
export type EndorsementStatus = 
  | "En revisión" 
  | "Por actualizar" 
  | "Actualizado" 
  | "Cancelado";

export type EndorsementType =
  | "Cambio de domicilio"
  | "Cancelación"
  | "Cambio de nombre";


export interface Endorsement {
  id: string;
  usuario: string;
  tipoEndoso: EndorsementType;
  fechaRegistro: string;
  estatus: EndorsementStatus;
}

export interface EndorsementFilters {
  status: EndorsementStatus | "Todos" | "" | null;
  searchTerm: string;
  order: "ASC" | "DESC";
}

export interface EndorsementTableProps {
  endorsements: Endorsement[];
  loading?: boolean;
  onSearchFilters: (key: string, value: string | string[] | null) => void;
  onPageChange?: (page: number) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
  };
}
