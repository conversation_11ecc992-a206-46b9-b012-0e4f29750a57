import { useState } from "react";
import promotionManagerService from "src/services/promotion-manager/promotion-manager.service";
import { UpdatePromotionDto } from "src/services/promotion-manager/dtos/update-promotion.dto";

export function useUpdatePromotion() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<unknown>(null);

  const updatePromotion = async (payload: UpdatePromotionDto) => {
    setLoading(true);
    setError(null);
    try {
      const response = await promotionManagerService.updatePromotion(payload);
      setData(response);
      return response;
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Error al actualizar la promoción"
      );
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    updatePromotion,
    loading,
    error,
    data,
  };
}
