"use client";
import { createContext, useEffect } from "react";
import { AuthProviderProps, AuthValuesType } from "./types";
import { LoginDto } from "src/services/auth/dtos/login.dto";
import { useState } from "react";
import AuthServices from "src/services/auth/auth.service";
import { useRouter, usePathname } from "next/navigation";

import { AxiosError } from "axios";
import Cookies from "js-cookie";

const defaultProvider: AuthValuesType = {
	loading: false,
	setLoading: () => {},
	onLogin: () => Promise.resolve(),
	onLogout: () => Promise.resolve(),
};

const AuthContext = createContext<AuthValuesType>(defaultProvider);

const AuthProvider = ({ children }: AuthProviderProps) => {
	// ** next navigation hook
	const router = useRouter();
	const pathname = usePathname();

	const [loading, setLoading] = useState<boolean>(defaultProvider.loading);
	const [error, setError] = useState();

	const handleOnLogin = async (params: LoginDto) => {
		const authServices = new AuthServices();

		try {
			const response = await authServices.login(params);
			if (response) {
				window.localStorage.setItem("token", response.token);
				Cookies.set("token", response.token);
				router.replace("/dashboard");
			}
		} catch (err) {
			if (err instanceof AxiosError) {
				setError(error);
			}
		}
	};

	const handleOnLogout = async () => {
		window.localStorage.removeItem("token");
		Cookies.remove("token");
		router.replace("/login");
	};

	useEffect(() => {
		const authToken = Cookies.get("token");
		if (!authToken && pathname === "/login") {
			router.replace("/login");
		}
	}, [router, pathname]);

	const values = {
		loading,
		setLoading,
		onLogin: handleOnLogin,
		onLogout: handleOnLogout,
	};

	return <AuthContext.Provider value={values}>{children}</AuthContext.Provider>;
};

export { AuthContext, AuthProvider };
